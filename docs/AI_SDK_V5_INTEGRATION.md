# AI SDK V5 Integration Documentation

## Overview

This project has been integrated with AI SDK V5 (beta), providing comprehensive AI capabilities including:

- **Streaming Chat**: Real-time conversational AI with tool calling
- **Structured Data Generation**: Generate typed objects from natural language
- **Text Embeddings**: Vector embeddings for semantic search and similarity
- **Tool Calling**: Multi-step AI operations with custom tools
- **Error Handling**: Robust error handling with retry mechanisms

## Architecture

### Core Components

1. **API Routes** (`src/app/api/`)
   - `/api/chat` - Streaming chat with tool calling
   - `/api/generate-object` - Structured data generation
   - `/api/embeddings` - Text embedding generation

2. **UI Components** (`src/components/`)
   - `ai-chat.tsx` - Interactive chat interface with tool visualization

3. **Utilities** (`src/lib/`)
   - `ai-error-handler.ts` - Centralized error handling for AI operations

4. **Demo Page** (`src/app/ai-demo/`)
   - Comprehensive demo showcasing all AI SDK features

## Features

### 1. Streaming Chat with Tools

The chat API supports real-time streaming responses with tool calling capabilities:

**Available Tools:**
- **Weather Tool**: Get weather information for any location
- **Calculator**: Perform mathematical calculations
- **Temperature Converter**: Convert between Fahrenheit and Celsius

**Usage Example:**
```typescript
const { messages, sendMessage } = useChat();
sendMessage({ text: "What's the weather in New York in Celsius?" });
```

### 2. Structured Data Generation

Generate typed objects from natural language descriptions:

**Supported Types:**
- **Person**: Generate fictional person profiles
- **Recipe**: Create detailed cooking recipes

**API Usage:**
```bash
POST /api/generate-object
{
  "prompt": "A software engineer from Tokyo who loves hiking",
  "type": "person"
}
```

### 3. Text Embeddings

Generate vector embeddings for text using OpenAI's embedding models:

**API Usage:**
```bash
POST /api/embeddings
{
  "text": "Your text to embed"
}
```

## Configuration

### Environment Variables

Create a `.env.local` file with your API keys:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional (for additional providers)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key_here

# AI SDK Configuration
AI_SDK_LOG_LEVEL=info
```

### Dependencies

The following packages are installed:

```json
{
  "ai": "5.0.0-beta.31",
  "@ai-sdk/react": "2.0.0-beta.31",
  "@ai-sdk/openai": "2.0.0-beta.14"
}
```

## Usage Guide

### 1. Basic Chat Implementation

```typescript
'use client';
import { useChat } from '@ai-sdk/react';

export default function ChatComponent() {
  const { messages, sendMessage, isLoading } = useChat();
  
  return (
    <div>
      {messages.map(message => (
        <div key={message.id}>
          {message.parts.map((part, i) => {
            switch (part.type) {
              case 'text':
                return <div>{part.text}</div>;
              case 'tool-weather':
                return <div>Weather: {JSON.stringify(part.result)}</div>;
              default:
                return null;
            }
          })}
        </div>
      ))}
    </div>
  );
}
```

### 2. Server-Side Text Generation

```typescript
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';

const result = await generateText({
  model: openai('gpt-4o-mini'),
  prompt: 'Write a haiku about programming',
});

console.log(result.text);
```

### 3. Tool Definition

```typescript
import { tool } from 'ai';
import { z } from 'zod';

const weatherTool = tool({
  description: 'Get weather information',
  inputSchema: z.object({
    location: z.string().describe('The location to get weather for'),
  }),
  execute: async ({ location }) => {
    // Your weather API logic here
    return { location, temperature: 72, conditions: 'sunny' };
  },
});
```

## Error Handling

The integration includes comprehensive error handling:

```typescript
import { handleAIError, withRetry } from '@/lib/ai-error-handler';

try {
  const result = await withRetry(async () => 
    generateText({ model: openai('gpt-4o-mini'), prompt: 'Hello' })
  );
} catch (error) {
  const errorResponse = handleAIError(error);
  console.error('AI Error:', errorResponse);
}
```

## Testing

Run the AI functionality tests:

```bash
# Run integration tests (requires valid API keys)
npm test src/lib/__tests__/ai-functionality.test.ts

# Test tool functionality manually
npm run dev
# Navigate to http://localhost:3000/ai-demo
```

## Demo

Visit `/ai-demo` to see all features in action:

1. **Chat & Tools Tab**: Interactive chat with weather, calculator, and temperature conversion tools
2. **Structured Data Tab**: Generate person profiles and recipes
3. **Embeddings Tab**: Generate vector embeddings for text

## Best Practices

1. **API Key Security**: Never commit API keys to version control
2. **Error Handling**: Always wrap AI calls in try-catch blocks
3. **Rate Limiting**: Implement rate limiting for production use
4. **Streaming**: Use streaming for better user experience in chat applications
5. **Tool Validation**: Validate tool inputs and outputs thoroughly
6. **Cost Management**: Monitor token usage and implement usage limits

## Troubleshooting

### Common Issues

1. **API Key Not Found**: Ensure `.env.local` is created and contains valid API keys
2. **Streaming Issues**: Check that the client properly handles streaming responses
3. **Tool Errors**: Verify tool schemas match the expected input format
4. **Rate Limits**: Implement exponential backoff for rate limit errors

### Debug Mode

Enable debug logging:

```env
AI_SDK_LOG_LEVEL=debug
```

## Next Steps

1. **Add More Providers**: Integrate Anthropic, Google, or other providers
2. **Custom Tools**: Create domain-specific tools for your use case
3. **RAG Implementation**: Add retrieval-augmented generation
4. **Multi-modal**: Add image and audio processing capabilities
5. **Agent Framework**: Build complex AI agents with multiple tools

## Resources

- [AI SDK V5 Documentation](https://v5.ai-sdk.dev)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [Vercel AI SDK GitHub](https://github.com/vercel/ai)
