# AI SDK UI Components Documentation

## Overview

This document provides comprehensive documentation for the AI SDK UI components built for Omnispace. These components provide advanced AI capabilities including chat interfaces, object generation, completion tools, and workspace-specific integrations.

## Architecture

### Core Components

1. **Advanced Chat Components** (`src/components/ai/`)
   - `AdvancedChat` - Full-featured chat interface with persistence
   - `MessageRenderer` - Renders individual messages with tool support
   - `ToolCallRenderer` - Displays tool executions with specialized UI
   - `DataPartRenderer` - Handles streaming data visualization

2. **Generation Components**
   - `ObjectGeneration` - Structured object generation with schemas
   - `Completion` - Text completion with customizable settings
   - `StreamingData` - Real-time data streaming interface

3. **Workspace Tools** (`src/components/ai/workspace-tools/`)
   - `WorkspaceTerminalTool` - Terminal interface for Docker containers
   - Additional workspace-specific tools for Omnispace integration

4. **Utilities and Hooks** (`src/hooks/`, `src/lib/`)
   - `useEnhancedChat` - Extended chat hook with analytics
   - `chat-storage.ts` - Message persistence system
   - `ai-utils.ts` - Utility functions for AI operations

## Component Usage

### AdvancedChat

The main chat interface component with full AI SDK V5 integration.

```tsx
import { AdvancedChat } from '@/components/ai/advanced-chat';

<AdvancedChat
  id="workspace-chat"
  api="/api/workspace-chat"
  title="Workspace AI Assistant"
  subtitle="Connected to your development environment"
  placeholder="Ask me to help with your workspace..."
  showTimestamps={true}
  showTokenCount={true}
  allowFileUpload={true}
  allowRegenerate={true}
  allowDelete={true}
  allowCopy={true}
  allowExport={true}
  onMessageSent={(message) => console.log('Sent:', message)}
  onMessageReceived={(message) => console.log('Received:', message)}
/>
```

**Props:**
- `id` - Unique identifier for the chat session
- `api` - API endpoint for chat requests
- `title` - Chat interface title
- `subtitle` - Subtitle text
- `placeholder` - Input placeholder text
- `showTimestamps` - Display message timestamps
- `showTokenCount` - Show token usage information
- `allowFileUpload` - Enable file upload functionality
- `allowRegenerate` - Allow message regeneration
- `allowDelete` - Allow message deletion
- `allowCopy` - Enable copy functionality
- `allowExport` - Allow chat export
- `onMessageSent` - Callback for sent messages
- `onMessageReceived` - Callback for received messages

### ObjectGeneration

Generate structured objects from natural language descriptions.

```tsx
import { ObjectGeneration } from '@/components/ai/object-generation';

<ObjectGeneration
  api="/api/generate-object"
  defaultType="person"
  showTypeSelector={true}
  showProgress={true}
  allowExport={true}
  onGenerated={(object, type) => {
    console.log('Generated:', { type, object });
  }}
/>
```

**Supported Types:**
- `person` - Generate person profiles
- `recipe` - Create detailed recipes
- `article` - Generate article structures

### Completion

Text completion with streaming support and customizable settings.

```tsx
import { Completion } from '@/components/ai/completion';

<Completion
  api="/api/completion"
  placeholder="Enter your prompt..."
  maxLength={2000}
  showSettings={true}
  showStats={true}
  allowExport={true}
  onComplete={(completion, prompt) => {
    console.log('Completed:', { prompt, completion });
  }}
/>
```

### StreamingData

Handle real-time streaming data with event management.

```tsx
import { StreamingData } from '@/components/ai/streaming-data';

<StreamingData
  endpoint="/api/workspace-stream"
  autoStart={false}
  maxRetries={3}
  retryDelay={1000}
  onData={(data) => console.log('Data:', data)}
  onError={(error) => console.error('Error:', error)}
  onComplete={() => console.log('Stream complete')}
/>
```

## Workspace Integration

### Terminal Tool

The `WorkspaceTerminalTool` provides a terminal interface for executing commands in Docker containers.

```tsx
import { WorkspaceTerminalTool } from '@/components/ai/workspace-tools/terminal-tool';

<WorkspaceTerminalTool
  toolCall={{
    args: {
      command: 'ls -la',
      workingDirectory: '/workspace'
    }
  }}
  onUpdate={(data) => console.log('Terminal update:', data)}
  onError={(error) => console.error('Terminal error:', error)}
/>
```

**Features:**
- Execute terminal commands
- Real-time output display
- Working directory management
- Command history
- Export session data

### API Integration

The workspace chat API (`/api/workspace-chat`) provides specialized tools for Omnispace:

1. **Terminal Tool** - Execute commands in Docker containers
2. **File Operations** - Read, write, create, delete files
3. **VNC Control** - Control desktop sessions
4. **VM Management** - Manage Firecracker VMs and containers

## Custom Hooks

### useEnhancedChat

Extended chat functionality with analytics and persistence.

```tsx
import { useEnhancedChat } from '@/hooks/use-enhanced-chat';
import { createChatStorage } from '@/lib/chat-storage';

const chatStorage = createChatStorage();

const chat = useEnhancedChat({
  id: 'workspace-chat',
  api: '/api/workspace-chat',
  persistence: {
    storage: chatStorage,
    autoSave: true,
    saveInterval: 30000,
  },
  maxMessages: 100,
  enableAnalytics: true,
  rateLimitConfig: {
    maxRequestsPerMinute: 20,
    maxRequestsPerHour: 200,
  },
});

// Access enhanced features
console.log('Message count:', chat.getMessageCount());
console.log('Token count:', chat.getTokenCount());
console.log('Average response time:', chat.getAverageResponseTime());
console.log('Most used tool:', chat.getMostUsedTool());
```

## Message Persistence

The chat storage system provides persistent message storage with IndexedDB and localStorage fallback.

```tsx
import { createChatStorage } from '@/lib/chat-storage';

const storage = createChatStorage();

// Save chat
await storage.saveChat('chat-id', messages);

// Load chat
const messages = await storage.loadChat('chat-id');

// List chats
const chats = await storage.listChats();

// Delete chat
await storage.deleteChat('chat-id');
```

## Error Handling

Comprehensive error handling with retry mechanisms and user-friendly error displays.

```tsx
import { ErrorDisplay } from '@/components/ai/error-display';

<ErrorDisplay
  error={error}
  onRetry={() => retryOperation()}
  onDismiss={() => setError(null)}
  showDetails={true}
/>
```

## Utilities

### AI Utilities

```tsx
import {
  extractTextFromMessage,
  extractToolCallsFromMessage,
  getConversationStats,
  formatTimestamp,
  validateMessage,
} from '@/lib/ai-utils';

// Extract text from message
const text = extractTextFromMessage(message);

// Get conversation statistics
const stats = getConversationStats(messages);

// Format timestamp
const formatted = formatTimestamp(Date.now(), 'relative');
```

## Best Practices

1. **Error Handling**: Always wrap AI operations in try-catch blocks
2. **Rate Limiting**: Implement rate limiting for production use
3. **Message Validation**: Validate message structure before processing
4. **Token Management**: Monitor token usage and implement limits
5. **Persistence**: Use auto-save for important conversations
6. **Security**: Validate and sanitize all user inputs
7. **Performance**: Use streaming for better user experience

## Testing

```bash
# Run component tests
npm test src/components/ai/

# Run integration tests
npm test src/lib/__tests__/

# Run workspace integration tests
npm test src/app/workspace-ai/
```

## Deployment Considerations

1. **Environment Variables**: Ensure all API keys are properly configured
2. **CORS**: Configure CORS for API endpoints
3. **Rate Limits**: Set appropriate rate limits for production
4. **Monitoring**: Implement logging and monitoring for AI operations
5. **Caching**: Consider caching for frequently used operations
6. **Security**: Implement proper authentication and authorization

## Troubleshooting

### Common Issues

1. **API Key Errors**: Check `.env.local` configuration
2. **Streaming Issues**: Verify endpoint supports Server-Sent Events
3. **Storage Issues**: Check IndexedDB availability and permissions
4. **Tool Errors**: Validate tool schemas and implementations
5. **Rate Limits**: Implement exponential backoff for retries

### Debug Mode

Enable debug logging:

```env
AI_SDK_LOG_LEVEL=debug
```

## Future Enhancements

1. **Multi-modal Support**: Add image and audio processing
2. **Advanced Tools**: Create more workspace-specific tools
3. **Agent Framework**: Build complex AI agents
4. **Real-time Collaboration**: Multi-user chat sessions
5. **Custom Models**: Support for additional AI providers

## Resources

- [AI SDK V5 Documentation](https://v5.ai-sdk.dev)
- [Omnispace Architecture](./ARCHITECTURE.md)
- [API Reference](./API_REFERENCE.md)
- [Component Examples](../src/app/workspace-ai/page.tsx)
