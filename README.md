# Omnispace

**Omnispace** is a modern web-based interface for managing and connecting to Firecracker microVM desktops via VNC. Built with Next.js, TypeScript, and shadcn/ui, it provides a seamless cloud desktop experience with minimal overhead.

## 🚀 Features

- **MicroVM Management**: Create, start, stop, and delete Firecracker microVMs
- **VNC Desktop Access**: Connect to VM desktops directly in the browser using noVNC
- **AI Integration (NEW!)**: Advanced AI capabilities powered by AI SDK V5
  - **Streaming Chat**: Real-time conversational AI with tool calling
  - **Structured Data Generation**: Generate typed objects from natural language
  - **Text Embeddings**: Vector embeddings for semantic search
  - **Tool Calling**: Multi-step AI operations with custom tools
- **Modern UI**: Clean, responsive interface built with shadcn/ui components
- **Real-time Status**: Live VM status monitoring and connection management
- **Resource Monitoring**: Track CPU, memory, and disk usage for each VM
- **Scalable Architecture**: Designed for both single-user and multi-tenant deployments

## 🏗️ Architecture

### Frontend
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **shadcn/ui** for component library
- **noVNC** for browser-based VNC client
- **Lucide React** for icons

### Backend Integration
- **WebSocket** connections for VNC
- **REST API** for VM management (to be implemented)
- **Firecracker VMM** for lightweight virtualization

## 📋 Prerequisites

- Node.js 18+ or 20+
- pnpm (recommended) or npm
- OpenAI API key (for AI features)
- Firecracker VMM (for backend integration)
- Linux host system (for Firecracker)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd omnispace
   ```

2. **Install dependencies**:
   ```bash
   pnpm install
   ```

3. **Run the development server**:
   ```bash
   pnpm dev
   ```

4. **Configure environment variables** (see Configuration section below)

5. **Open your browser** and navigate to:
   - `http://localhost:3000` - Main application (MicroVM management)
   - `http://localhost:3000/ai-demo` - AI features demo

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the project root:

```env
# AI SDK Configuration (Required for AI features)
OPENAI_API_KEY=your_openai_api_key_here

# Optional AI providers
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key_here

# AI SDK Settings
AI_SDK_LOG_LEVEL=info

# VNC Configuration
VNC_BASE_PORT=5900
VNC_HOST=localhost

# Firecracker Configuration
FIRECRACKER_SOCKET_PATH=/tmp/firecracker.socket
FIRECRACKER_KERNEL_PATH=/opt/firecracker/vmlinux.bin
FIRECRACKER_ROOTFS_PATH=/opt/firecracker/rootfs.img

# API Configuration
API_BASE_URL=http://localhost:3001
WEBSOCKET_URL=ws://localhost:3001
```

## 🖥️ VM Templates

Omnispace supports various VM templates:

### Ubuntu Desktop
- **OS**: Ubuntu 22.04 LTS with GNOME
- **Default Resources**: 2 CPU, 2GB RAM, 20GB disk
- **VNC Server**: TigerVNC

### Development Environment
- **OS**: Ubuntu 22.04 with development tools
- **Default Resources**: 4 CPU, 4GB RAM, 40GB disk
- **Includes**: VS Code, Docker, Git, Node.js

### Minimal Desktop
- **OS**: Alpine Linux with XFCE
- **Default Resources**: 1 CPU, 1GB RAM, 10GB disk
- **VNC Server**: x11vnc

## 🤖 AI Integration (AI SDK V5)

Omnispace now includes advanced AI capabilities powered by AI SDK V5 (beta):

### Available AI Tools
- **Weather Tool**: Get weather information for any location
- **Calculator**: Perform mathematical calculations
- **Temperature Converter**: Convert between Fahrenheit and Celsius

### AI API Endpoints

```typescript
// Streaming chat with tool calling
POST /api/chat
Body: { messages: UIMessage[] }
Response: Streaming text with tool calls

// Generate structured data
POST /api/generate-object
Body: { prompt: string, type: 'person' | 'recipe' }
Response: { success: true, data: object, usage: TokenUsage }

// Generate text embeddings
POST /api/embeddings
Body: { text: string } | { texts: string[] }
Response: { success: true, embedding: number[], dimensions: number }
```

### Example Usage

**Chat with AI Tools:**
```typescript
import { useChat } from '@ai-sdk/react';

const { messages, sendMessage } = useChat();
sendMessage({ text: "What's the weather in New York in Celsius?" });
```

**Generate Structured Data:**
```bash
curl -X POST http://localhost:3000/api/generate-object \
  -H "Content-Type: application/json" \
  -d '{"prompt": "A software engineer from Tokyo", "type": "person"}'
```

### AI Demo & Workspace Integration
Visit `/ai-demo` to explore basic AI features:
- Interactive chat with tool calling
- Structured data generation (persons, recipes)
- Text embedding generation

Visit `/workspace-ai` for advanced workspace integration:
- **Workspace AI Assistant**: AI-powered workspace management
- **Terminal AI**: Natural language terminal commands
- **File AI**: Intelligent file operations and code generation
- **Automation**: Workflow automation for development tasks
- **Live Data**: Real-time workspace monitoring and streaming

## 🔌 API Integration

### VM Management Endpoints

```typescript
// Get all VMs
GET /api/vms
Response: MicroVM[]

// Create new VM
POST /api/vms
Body: VMConfiguration
Response: MicroVM

// Start VM
POST /api/vms/:id/start
Response: { status: 'starting' }

// Stop VM
POST /api/vms/:id/stop
Response: { status: 'stopping' }

// Delete VM
DELETE /api/vms/:id
Response: { deleted: true }

// Get VNC connection info
GET /api/vms/:id/vnc
Response: VNCConnection
```

## 🚀 Deployment

### Development
```bash
pnpm dev
```

### Production Build
```bash
pnpm build
pnpm start
```

### Docker Deployment
```bash
# Build image
docker build -t omnispace .

# Run container
docker run -p 3000:3000 omnispace
```

## 🔐 Security Considerations

- **VNC Authentication**: Configure VNC passwords for each VM
- **Network Isolation**: Use proper network namespaces for VM isolation
- **Access Control**: Implement user authentication and authorization
- **Resource Limits**: Set appropriate CPU, memory, and disk quotas
- **Firewall Rules**: Configure iptables for VM network access

## 🛠️ Backend Setup (Firecracker)

To fully utilize Omnispace, you'll need a backend service that manages Firecracker VMs:

### Prerequisites
```bash
# Install Firecracker
curl -LOJ https://github.com/firecracker-microvm/firecracker/releases/latest/download/firecracker-v1.4.1-x86_64.tgz
tar -xzf firecracker-v1.4.1-x86_64.tgz
sudo cp release-v1.4.1-x86_64/firecracker-v1.4.1-x86_64 /usr/local/bin/firecracker

# Install kernel and rootfs images
mkdir -p /opt/firecracker
# Download kernel and rootfs images
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [Firecracker](https://firecracker-microvm.github.io/) - Lightweight virtualization technology
- [noVNC](https://novnc.com/) - HTML5 VNC client
- [shadcn/ui](https://ui.shadcn.com/) - Beautiful UI components
- [Next.js](https://nextjs.org/) - React framework for production

---

**Built with ❤️ for the cloud-native desktop experience**
