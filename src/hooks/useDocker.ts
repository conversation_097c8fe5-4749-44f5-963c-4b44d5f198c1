import { useState, useEffect, useCallback } from 'react';
import { ContainerInfo, CreateContainerOptions } from '@/services/docker';

interface UseDockerReturn {
  containers: ContainerInfo[];
  loading: boolean;
  error: string | null;
  systemInfo: any;
  connected: boolean;
  refreshContainers: () => Promise<void>;
  createContainer: (options: CreateContainerOptions) => Promise<string>;
  startContainer: (id: string) => Promise<void>;
  stopContainer: (id: string, timeout?: number) => Promise<void>;
  restartContainer: (id: string) => Promise<void>;
  removeContainer: (id: string, force?: boolean) => Promise<void>;
  getContainerLogs: (id: string, tail?: number) => Promise<string>;
  getContainerStats: (id: string) => Promise<any>;
  execInContainer: (id: string, cmd: string[]) => Promise<string>;
  pullImage: (imageName: string) => Promise<void>;
  getSystemInfo: () => Promise<void>;
}

export function useDocker(): UseDockerReturn {
  const [containers, setContainers] = useState<ContainerInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [systemInfo, setSystemInfo] = useState<any>(null);
  const [connected, setConnected] = useState(false);

  const handleApiError = (error: any) => {
    console.error('Docker API error:', error);
    const message = error?.message || 'An unknown error occurred';
    setError(message);
    throw new Error(message);
  };

  const apiCall = async (url: string, options: RequestInit = {}) => {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.message || 'API call failed');
      }

      return data;
    } catch (error) {
      handleApiError(error);
    }
  };

  const refreshContainers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiCall('/api/containers');
      setContainers(response.data);
    } catch (error) {
      // Error is already handled in apiCall
    } finally {
      setLoading(false);
    }
  }, []);

  const getSystemInfo = useCallback(async () => {
    try {
      const response = await apiCall('/api/system');
      setSystemInfo(response.data);
      setConnected(response.data.connected);
    } catch (error) {
      setConnected(false);
      setSystemInfo(null);
    }
  }, []);

  const createContainer = useCallback(async (options: CreateContainerOptions): Promise<string> => {
    const response = await apiCall('/api/containers', {
      method: 'POST',
      body: JSON.stringify(options),
    });
    
    await refreshContainers();
    return response.data.id;
  }, [refreshContainers]);

  const startContainer = useCallback(async (id: string): Promise<void> => {
    await apiCall(`/api/containers/${id}/start`, {
      method: 'POST',
    });
    
    await refreshContainers();
  }, [refreshContainers]);

  const stopContainer = useCallback(async (id: string, timeout = 10): Promise<void> => {
    await apiCall(`/api/containers/${id}/stop?timeout=${timeout}`, {
      method: 'POST',
    });
    
    await refreshContainers();
  }, [refreshContainers]);

  const restartContainer = useCallback(async (id: string): Promise<void> => {
    await apiCall(`/api/containers/${id}/restart`, {
      method: 'POST',
    });
    
    await refreshContainers();
  }, [refreshContainers]);

  const removeContainer = useCallback(async (id: string, force = false): Promise<void> => {
    await apiCall(`/api/containers/${id}?force=${force}`, {
      method: 'DELETE',
    });
    
    await refreshContainers();
  }, [refreshContainers]);

  const getContainerLogs = useCallback(async (id: string, tail = 100): Promise<string> => {
    const response = await apiCall(`/api/containers/${id}/logs?tail=${tail}`);
    return response.data.logs;
  }, []);

  const getContainerStats = useCallback(async (id: string): Promise<any> => {
    const response = await apiCall(`/api/containers/${id}/stats`);
    return response.data.stats;
  }, []);

  const execInContainer = useCallback(async (id: string, cmd: string[]): Promise<string> => {
    const response = await apiCall(`/api/containers/${id}/exec`, {
      method: 'POST',
      body: JSON.stringify({ cmd }),
    });
    
    return response.data.output;
  }, []);

  const pullImage = useCallback(async (imageName: string): Promise<void> => {
    await apiCall('/api/images', {
      method: 'POST',
      body: JSON.stringify({ image: imageName }),
    });
  }, []);

  // Initialize data on mount
  useEffect(() => {
    const initializeData = async () => {
      await getSystemInfo();
      await refreshContainers();
    };
    
    initializeData();
  }, [getSystemInfo, refreshContainers]);

  return {
    containers,
    loading,
    error,
    systemInfo,
    connected,
    refreshContainers,
    createContainer,
    startContainer,
    stopContainer,
    restartContainer,
    removeContainer,
    getContainerLogs,
    getContainerStats,
    execInContainer,
    pullImage,
    getSystemInfo,
  };
}

// Utility hook for managing a specific container
export function useContainer(containerId: string) {
  const [container, setContainer] = useState<ContainerInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshContainer = useCallback(async () => {
    if (!containerId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/containers/${containerId}`);
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Failed to fetch container');
      }
      
      setContainer(data.data);
    } catch (error) {
      console.error('Error fetching container:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setContainer(null);
    } finally {
      setLoading(false);
    }
  }, [containerId]);

  useEffect(() => {
    refreshContainer();
  }, [refreshContainer]);

  return {
    container,
    loading,
    error,
    refreshContainer,
  };
}
