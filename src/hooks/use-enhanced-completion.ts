'use client';

import { useCompletion } from '@ai-sdk/react';
import { useState, useCallback, useRef, useEffect } from 'react';
import { CompletionConfig } from '@/types/ai-sdk-ui';

interface EnhancedCompletionConfig extends CompletionConfig {
  templates?: Record<string, string>;
  autoSave?: boolean;
  maxHistory?: number;
  enableSuggestions?: boolean;
  customTransforms?: Array<(text: string) => string>;
}

interface CompletionHistory {
  id: string;
  prompt: string;
  completion: string;
  timestamp: number;
  settings: any;
}

interface CompletionSuggestion {
  text: string;
  confidence: number;
  type: 'continuation' | 'improvement' | 'alternative';
}

export function useEnhancedCompletion(config: EnhancedCompletionConfig = {}) {
  const [history, setHistory] = useState<CompletionHistory[]>([]);
  const [suggestions, setSuggestions] = useState<CompletionSuggestion[]>([]);
  const [currentTemplate, setCurrentTemplate] = useState<string>('');
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false);
  const [notification, setNotification] = useState<string | null>(null);
  
  const completionIdRef = useRef(0);

  const baseCompletion = useCompletion({
    ...config,
    onFinish: (prompt, completion) => {
      // Add to history
      const historyEntry: CompletionHistory = {
        id: `completion-${++completionIdRef.current}`,
        prompt,
        completion,
        timestamp: Date.now(),
        settings: {
          temperature: config.body?.temperature,
          maxTokens: config.body?.max_tokens,
        },
      };
      
      setHistory(prev => {
        const newHistory = [historyEntry, ...prev];
        return config.maxHistory 
          ? newHistory.slice(0, config.maxHistory)
          : newHistory;
      });

      // Auto-save if enabled
      if (config.autoSave) {
        localStorage.setItem('completion-history', JSON.stringify(history));
      }

      // Generate suggestions if enabled
      if (config.enableSuggestions && completion) {
        generateSuggestions(completion);
      }

      config.onFinish?.(prompt, completion);
    },
  });

  // Load history from localStorage on mount
  useEffect(() => {
    if (config.autoSave) {
      const savedHistory = localStorage.getItem('completion-history');
      if (savedHistory) {
        try {
          setHistory(JSON.parse(savedHistory));
        } catch (error) {
          console.error('Failed to load completion history:', error);
        }
      }
    }
  }, [config.autoSave]);

  // Generate suggestions based on current completion
  const generateSuggestions = useCallback(async (text: string) => {
    if (!config.enableSuggestions) return;
    
    setIsGeneratingSuggestions(true);
    
    try {
      // Simple suggestion generation (in a real app, this might call an API)
      const suggestions: CompletionSuggestion[] = [];
      
      // Continuation suggestion
      if (text.length > 50) {
        suggestions.push({
          text: text + ' Additionally, consider...',
          confidence: 0.8,
          type: 'continuation',
        });
      }
      
      // Improvement suggestion
      if (text.includes('very') || text.includes('really')) {
        suggestions.push({
          text: text.replace(/very |really /g, ''),
          confidence: 0.7,
          type: 'improvement',
        });
      }
      
      // Alternative suggestion
      suggestions.push({
        text: `Here's an alternative approach: ${text.substring(0, 100)}...`,
        confidence: 0.6,
        type: 'alternative',
      });
      
      setSuggestions(suggestions);
    } catch (error) {
      console.error('Failed to generate suggestions:', error);
    } finally {
      setIsGeneratingSuggestions(false);
    }
  }, [config.enableSuggestions]);

  // Apply template to input
  const applyTemplate = useCallback((templateName: string, variables: Record<string, string> = {}) => {
    if (!config.templates || !config.templates[templateName]) return;
    
    let template = config.templates[templateName];
    
    // Replace variables in template
    Object.entries(variables).forEach(([key, value]) => {
      template = template.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    
    setCurrentTemplate(templateName);
    baseCompletion.setInput(template);
  }, [config.templates, baseCompletion.setInput]);

  // Apply custom transforms
  const applyTransforms = useCallback((text: string) => {
    if (!config.customTransforms) return text;
    
    return config.customTransforms.reduce((acc, transform) => {
      try {
        return transform(acc);
      } catch (error) {
        console.error('Transform failed:', error);
        return acc;
      }
    }, text);
  }, [config.customTransforms]);

  // Enhanced submit with transforms
  const submitWithTransforms = useCallback((prompt?: string) => {
    const inputText = prompt || baseCompletion.input;
    const transformedText = applyTransforms(inputText);
    
    if (transformedText !== inputText) {
      baseCompletion.setInput(transformedText);
    }
    
    baseCompletion.submit(transformedText);
  }, [baseCompletion.input, baseCompletion.setInput, baseCompletion.submit, applyTransforms]);

  // Retry with different settings
  const retryWithSettings = useCallback((settings: any) => {
    const newConfig = { ...config, body: { ...config.body, ...settings } };
    // This would require reinitializing the hook, so we'll just show a notification
    setNotification('Settings updated. Please regenerate to apply changes.');
    setTimeout(() => setNotification(null), 3000);
  }, [config]);

  // Get completion from history
  const getFromHistory = useCallback((id: string) => {
    const entry = history.find(h => h.id === id);
    if (entry) {
      baseCompletion.setInput(entry.prompt);
      // Note: We can't directly set the completion, so we'll just set the input
      setNotification('Prompt loaded from history');
      setTimeout(() => setNotification(null), 2000);
    }
  }, [history, baseCompletion.setInput]);

  // Delete from history
  const deleteFromHistory = useCallback((id: string) => {
    setHistory(prev => prev.filter(h => h.id !== id));
    if (config.autoSave) {
      const newHistory = history.filter(h => h.id !== id);
      localStorage.setItem('completion-history', JSON.stringify(newHistory));
    }
  }, [history, config.autoSave]);

  // Clear history
  const clearHistory = useCallback(() => {
    setHistory([]);
    if (config.autoSave) {
      localStorage.removeItem('completion-history');
    }
    setNotification('History cleared');
    setTimeout(() => setNotification(null), 2000);
  }, [config.autoSave]);

  // Export history
  const exportHistory = useCallback(() => {
    const exportData = {
      history,
      exportedAt: new Date().toISOString(),
      totalCompletions: history.length,
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `completion-history-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [history]);

  // Apply suggestion
  const applySuggestion = useCallback((suggestion: CompletionSuggestion) => {
    baseCompletion.setInput(suggestion.text);
    setSuggestions([]);
    setNotification(`Applied ${suggestion.type} suggestion`);
    setTimeout(() => setNotification(null), 2000);
  }, [baseCompletion.setInput]);

  // Get statistics
  const getStatistics = useCallback(() => {
    const totalCompletions = history.length;
    const totalCharacters = history.reduce((acc, h) => acc + h.completion.length, 0);
    const averageLength = totalCompletions > 0 ? totalCharacters / totalCompletions : 0;
    
    const templateUsage = history.reduce((acc, h) => {
      // This is a simplified way to detect template usage
      const template = Object.keys(config.templates || {}).find(t => 
        h.prompt.includes(config.templates![t].substring(0, 20))
      );
      if (template) {
        acc[template] = (acc[template] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);
    
    return {
      totalCompletions,
      totalCharacters,
      averageLength: Math.round(averageLength),
      templateUsage,
      mostUsedTemplate: Object.keys(templateUsage).reduce((a, b) => 
        templateUsage[a] > templateUsage[b] ? a : b, ''
      ),
    };
  }, [history, config.templates]);

  return {
    // Base completion functionality
    ...baseCompletion,
    submit: submitWithTransforms,
    
    // Enhanced functionality
    applyTemplate,
    applyTransforms,
    retryWithSettings,
    
    // History management
    history,
    getFromHistory,
    deleteFromHistory,
    clearHistory,
    exportHistory,
    
    // Suggestions
    suggestions,
    isGeneratingSuggestions,
    applySuggestion,
    generateSuggestions: () => generateSuggestions(baseCompletion.completion),
    
    // State
    currentTemplate,
    notification,
    
    // Statistics
    getStatistics,
    
    // Utility methods
    getCompletionCount: () => history.length,
    getAverageCompletionLength: () => {
      const stats = getStatistics();
      return stats.averageLength;
    },
  };
}
