'use client';

import { useChat } from '@ai-sdk/react';
import { useState, useCallback, useEffect, useRef } from 'react';
import { CustomUIMessage, ChatConfig, MessagePersistenceConfig } from '@/types/ai-sdk-ui';

interface EnhancedChatConfig extends ChatConfig {
  persistence?: MessagePersistenceConfig;
  autoSave?: boolean;
  maxMessages?: number;
  enableAnalytics?: boolean;
  rateLimitConfig?: {
    maxRequestsPerMinute: number;
    maxRequestsPerHour: number;
  };
}

interface ChatAnalytics {
  totalMessages: number;
  totalTokens: number;
  averageResponseTime: number;
  errorRate: number;
  mostUsedTools: Record<string, number>;
}

export function useEnhancedChat(config: EnhancedChatConfig = {}) {
  const [analytics, setAnalytics] = useState<ChatAnalytics>({
    totalMessages: 0,
    totalTokens: 0,
    averageResponseTime: 0,
    errorRate: 0,
    mostUsedTools: {},
  });
  
  const [requestTimes, setRequestTimes] = useState<number[]>([]);
  const [errors, setErrors] = useState<Error[]>([]);
  const [isRateLimited, setIsRateLimited] = useState(false);
  const [notification, setNotification] = useState<string | null>(null);
  
  const requestTimestamps = useRef<number[]>([]);
  const startTimeRef = useRef<number>(0);

  // Rate limiting check
  const checkRateLimit = useCallback(() => {
    if (!config.rateLimitConfig) return true;
    
    const now = Date.now();
    const oneMinuteAgo = now - 60 * 1000;
    const oneHourAgo = now - 60 * 60 * 1000;
    
    // Clean old timestamps
    requestTimestamps.current = requestTimestamps.current.filter(
      timestamp => timestamp > oneHourAgo
    );
    
    const recentRequests = requestTimestamps.current.filter(
      timestamp => timestamp > oneMinuteAgo
    );
    
    const hourlyRequests = requestTimestamps.current.length;
    
    if (recentRequests.length >= config.rateLimitConfig.maxRequestsPerMinute) {
      setIsRateLimited(true);
      setNotification('Rate limit exceeded. Please wait a moment.');
      setTimeout(() => {
        setIsRateLimited(false);
        setNotification(null);
      }, 60000);
      return false;
    }
    
    if (hourlyRequests >= config.rateLimitConfig.maxRequestsPerHour) {
      setIsRateLimited(true);
      setNotification('Hourly rate limit exceeded. Please try again later.');
      return false;
    }
    
    return true;
  }, [config.rateLimitConfig]);

  const baseChat = useChat({
    ...config,
    onFinish: (message, options) => {
      const endTime = Date.now();
      const responseTime = endTime - startTimeRef.current;
      
      // Update analytics
      setAnalytics(prev => {
        const newTotalMessages = prev.totalMessages + 1;
        const newTotalTokens = prev.totalTokens + (options.usage?.totalTokens || 0);
        const newRequestTimes = [...requestTimes, responseTime];
        const newAverageResponseTime = newRequestTimes.reduce((a, b) => a + b, 0) / newRequestTimes.length;
        
        // Track tool usage
        const newMostUsedTools = { ...prev.mostUsedTools };
        if (message.parts) {
          message.parts.forEach(part => {
            if (part.type.startsWith('tool-')) {
              const toolName = part.type.replace('tool-', '');
              newMostUsedTools[toolName] = (newMostUsedTools[toolName] || 0) + 1;
            }
          });
        }
        
        return {
          totalMessages: newTotalMessages,
          totalTokens: newTotalTokens,
          averageResponseTime: newAverageResponseTime,
          errorRate: errors.length / newTotalMessages,
          mostUsedTools: newMostUsedTools,
        };
      });
      
      setRequestTimes(prev => [...prev.slice(-19), responseTime]); // Keep last 20
      
      // Auto-save if enabled
      if (config.autoSave && config.persistence) {
        config.persistence.storage.saveChat(
          config.id || 'default',
          baseChat.messages as CustomUIMessage[]
        );
      }
      
      config.onFinish?.(message, options);
    },
    onError: (error) => {
      setErrors(prev => [...prev.slice(-9), error]); // Keep last 10 errors
      config.onError?.(error);
    },
  });

  // Enhanced sendMessage with rate limiting
  const sendMessage = useCallback((message: { text: string; files?: FileList | File[] }) => {
    if (!checkRateLimit()) return;
    
    startTimeRef.current = Date.now();
    requestTimestamps.current.push(startTimeRef.current);
    
    baseChat.sendMessage(message);
  }, [baseChat.sendMessage, checkRateLimit]);

  // Message management
  const deleteMessage = useCallback((messageId: string) => {
    const updatedMessages = baseChat.messages.filter(m => m.id !== messageId);
    baseChat.setMessages(updatedMessages);
  }, [baseChat.messages, baseChat.setMessages]);

  const editMessage = useCallback((messageId: string, newContent: string) => {
    const updatedMessages = baseChat.messages.map(m => {
      if (m.id === messageId) {
        return {
          ...m,
          parts: [{ type: 'text', text: newContent }]
        };
      }
      return m;
    });
    baseChat.setMessages(updatedMessages);
  }, [baseChat.messages, baseChat.setMessages]);

  const clearChat = useCallback(() => {
    baseChat.setMessages([]);
    setAnalytics({
      totalMessages: 0,
      totalTokens: 0,
      averageResponseTime: 0,
      errorRate: 0,
      mostUsedTools: {},
    });
    setRequestTimes([]);
    setErrors([]);
  }, [baseChat.setMessages]);

  // Persistence methods
  const saveChat = useCallback(async (chatId?: string) => {
    if (!config.persistence) return;
    
    const id = chatId || config.id || 'default';
    await config.persistence.storage.saveChat(id, baseChat.messages as CustomUIMessage[]);
    setNotification('Chat saved successfully');
    setTimeout(() => setNotification(null), 2000);
  }, [config.persistence, config.id, baseChat.messages]);

  const loadChat = useCallback(async (chatId: string) => {
    if (!config.persistence) return;
    
    try {
      const messages = await config.persistence.storage.loadChat(chatId);
      baseChat.setMessages(messages);
      setNotification('Chat loaded successfully');
      setTimeout(() => setNotification(null), 2000);
    } catch (error) {
      setNotification('Failed to load chat');
      setTimeout(() => setNotification(null), 3000);
    }
  }, [config.persistence, baseChat.setMessages]);

  const exportChat = useCallback(() => {
    const exportData = {
      id: config.id,
      messages: baseChat.messages,
      analytics,
      exportedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-${config.id || 'export'}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [config.id, baseChat.messages, analytics]);

  // Auto-save effect
  useEffect(() => {
    if (!config.autoSave || !config.persistence || baseChat.messages.length === 0) return;
    
    const saveInterval = setInterval(() => {
      config.persistence!.storage.saveChat(
        config.id || 'default',
        baseChat.messages as CustomUIMessage[]
      );
    }, config.persistence.saveInterval || 30000); // Default 30 seconds
    
    return () => clearInterval(saveInterval);
  }, [config.autoSave, config.persistence, config.id, baseChat.messages]);

  // Message limit enforcement
  useEffect(() => {
    if (!config.maxMessages || baseChat.messages.length <= config.maxMessages) return;
    
    const trimmedMessages = baseChat.messages.slice(-config.maxMessages);
    baseChat.setMessages(trimmedMessages);
  }, [config.maxMessages, baseChat.messages, baseChat.setMessages]);

  return {
    // Base chat functionality
    ...baseChat,
    sendMessage,
    
    // Enhanced functionality
    deleteMessage,
    editMessage,
    clearChat,
    saveChat,
    loadChat,
    exportChat,
    
    // Analytics and monitoring
    analytics,
    errors: errors.slice(-5), // Return last 5 errors
    isRateLimited,
    notification,
    
    // Utility methods
    getMessageCount: () => baseChat.messages.length,
    getTokenCount: () => analytics.totalTokens,
    getAverageResponseTime: () => analytics.averageResponseTime,
    getMostUsedTool: () => {
      const tools = analytics.mostUsedTools;
      return Object.keys(tools).reduce((a, b) => tools[a] > tools[b] ? a : b, '');
    },
  };
}
