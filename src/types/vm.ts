export interface MicroVM {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'starting' | 'stopping';
  cpu: number;
  memory: number; // in MB
  diskSize: number; // in GB
  vncPort: number;
  createdAt: Date;
  lastAccessed?: Date;
  ipAddress?: string;
  osType?: 'ubuntu' | 'debian' | 'alpine' | 'windows';
}

export interface VMConfiguration {
  name: string;
  cpu: number;
  memory: number;
  diskSize: number;
  osType?: string;
  networkConfig?: NetworkConfig;
}

export interface NetworkConfig {
  bridge?: string;
  ipAddress?: string;
  gateway?: string;
  dns?: string[];
}

export interface VNCConnection {
  url: string;
  port: number;
  password?: string;
  quality?: 'high' | 'medium' | 'low';
  compression?: boolean;
}

export interface FirecrackerConfig {
  kernelImagePath: string;
  rootfsImagePath: string;
  bootArgs: string;
  vcpuCount: number;
  memSizeMib: number;
  htEnabled: boolean;
  networkInterfaces: NetworkInterface[];
  drives: Drive[];
}

export interface NetworkInterface {
  ifaceId: string;
  guestMac: string;
  hostDevName: string;
}

export interface Drive {
  driveId: string;
  pathOnHost: string;
  isRootDevice: boolean;
  isReadOnly: boolean;
}
