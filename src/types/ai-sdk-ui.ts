import { UIMessage, UIDataTypes, InferUITools, ToolSet } from 'ai';
import { z } from 'zod';

// ============================================================================
// Core AI SDK UI Types
// ============================================================================

export type AIStatus = 'ready' | 'submitted' | 'streaming' | 'error';

export type AIError = Error | null;

// ============================================================================
// Custom Data Types for Streaming
// ============================================================================

export interface WeatherData {
  city: string;
  temperature?: number;
  weather?: string;
  humidity?: number;
  status: 'loading' | 'success' | 'error';
}

export interface NotificationData {
  message: string;
  level: 'info' | 'warning' | 'error' | 'success';
  timestamp?: number;
}

export interface ProgressData {
  current: number;
  total: number;
  message?: string;
  percentage: number;
}

export interface SourceData {
  id: string;
  url: string;
  title: string;
  description?: string;
  type: 'url' | 'document' | 'image';
}

export interface CodeData {
  language: string;
  code: string;
  filename?: string;
  status: 'generating' | 'complete' | 'error';
}

// ============================================================================
// Custom UI Message Types
// ============================================================================

export type CustomDataTypes = {
  weather: WeatherData;
  notification: NotificationData;
  progress: ProgressData;
  source: SourceData;
  code: CodeData;
};

export type CustomUIMessage = UIMessage<
  // Metadata type
  {
    createdAt?: number;
    model?: string;
    totalTokens?: number;
    userId?: string;
    sessionId?: string;
  },
  // Data types
  CustomDataTypes,
  // Tools type (will be inferred from actual tools)
  any
>;

// ============================================================================
// Hook Configuration Types
// ============================================================================

export interface ChatConfig {
  id?: string;
  api?: string;
  headers?: Record<string, string> | (() => Record<string, string>);
  body?: Record<string, any> | (() => Record<string, any>);
  credentials?: RequestCredentials | (() => RequestCredentials);
  initialMessages?: CustomUIMessage[];
  maxRetries?: number;
  retryDelay?: number;
  experimental_throttle?: number;
  onFinish?: (message: CustomUIMessage, options: { usage?: any; finishReason?: string }) => void;
  onError?: (error: Error) => void;
  onData?: (data: any) => void;
}

export interface CompletionConfig {
  api?: string;
  headers?: Record<string, string> | (() => Record<string, string>);
  body?: Record<string, any> | (() => Record<string, any>);
  credentials?: RequestCredentials | (() => RequestCredentials);
  experimental_throttle?: number;
  onResponse?: (response: Response) => void;
  onFinish?: (prompt: string, completion: string) => void;
  onError?: (error: Error) => void;
}

export interface ObjectConfig<T = any> {
  api?: string;
  schema: z.ZodSchema<T>;
  headers?: Record<string, string>;
  credentials?: RequestCredentials;
  onFinish?: (result: { object?: T; error?: Error }) => void;
  onError?: (error: Error) => void;
}

// ============================================================================
// Hook Return Types
// ============================================================================

export interface ChatHookReturn {
  messages: CustomUIMessage[];
  input: string;
  setInput: (input: string) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  sendMessage: (message: { text: string; files?: FileList | File[] }) => void;
  reload: () => void;
  stop: () => void;
  regenerate: () => void;
  setMessages: (messages: CustomUIMessage[]) => void;
  isLoading: boolean;
  status: AIStatus;
  error: AIError;
}

export interface CompletionHookReturn {
  completion: string;
  input: string;
  setInput: (input: string) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  submit: (prompt: string) => void;
  stop: () => void;
  isLoading: boolean;
  error: AIError;
}

export interface ObjectHookReturn<T = any> {
  object: T | undefined;
  submit: (input: string) => void;
  stop: () => void;
  isLoading: boolean;
  error: AIError;
}

// ============================================================================
// Component Props Types
// ============================================================================

export interface MessageProps {
  message: CustomUIMessage;
  isLast?: boolean;
  onRegenerate?: () => void;
  onDelete?: (messageId: string) => void;
  className?: string;
}

export interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  maxLength?: number;
  className?: string;
  allowFiles?: boolean;
  onFileSelect?: (files: FileList) => void;
}

export interface ToolCallProps {
  toolCall: any;
  isLoading?: boolean;
  error?: string;
  className?: string;
}

export interface ErrorDisplayProps {
  error: AIError;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
}

export interface LoadingIndicatorProps {
  status: AIStatus;
  message?: string;
  className?: string;
}

// ============================================================================
// Generative UI Types
// ============================================================================

export interface ToolComponentMapping {
  [toolName: string]: React.ComponentType<any>;
}

export interface GenerativeUIConfig {
  tools: ToolSet;
  components: ToolComponentMapping;
  fallbackComponent?: React.ComponentType<{ toolName: string; data: any }>;
}

// ============================================================================
// Message Persistence Types
// ============================================================================

export interface ChatStorage {
  saveChat: (chatId: string, messages: CustomUIMessage[]) => Promise<void>;
  loadChat: (chatId: string) => Promise<CustomUIMessage[]>;
  deleteChat: (chatId: string) => Promise<void>;
  listChats: () => Promise<{ id: string; title: string; updatedAt: number }[]>;
  createChat: () => Promise<string>;
}

export interface MessagePersistenceConfig {
  storage: ChatStorage;
  autoSave?: boolean;
  saveInterval?: number;
  maxMessages?: number;
}

// ============================================================================
// Transport Types
// ============================================================================

export interface TransportConfig {
  api: string;
  headers?: Record<string, string> | (() => Record<string, string>);
  body?: Record<string, any> | (() => Record<string, any>);
  credentials?: RequestCredentials | (() => RequestCredentials);
  prepareSendMessagesRequest?: (params: {
    messages: CustomUIMessage[];
    id?: string;
    trigger?: string;
    messageId?: string;
  }) => { body: any };
}

// ============================================================================
// Utility Types
// ============================================================================

export type MessagePart = CustomUIMessage['parts'][0];

export type TextPart = Extract<MessagePart, { type: 'text' }>;

export type ToolPart = Extract<MessagePart, { type: string }> & { type: `tool-${string}` };

export type DataPart = Extract<MessagePart, { type: string }> & { type: `data-${string}` };

export type FilePart = Extract<MessagePart, { type: 'file' }>;

// ============================================================================
// Event Types
// ============================================================================

export interface ChatEvent {
  type: 'message' | 'error' | 'status' | 'data';
  data: any;
  timestamp: number;
}

export interface MessageEvent extends ChatEvent {
  type: 'message';
  data: CustomUIMessage;
}

export interface ErrorEvent extends ChatEvent {
  type: 'error';
  data: Error;
}

export interface StatusEvent extends ChatEvent {
  type: 'status';
  data: AIStatus;
}

export interface DataEvent extends ChatEvent {
  type: 'data';
  data: any;
}

// ============================================================================
// Validation Schemas
// ============================================================================

export const messageSchema = z.object({
  id: z.string(),
  role: z.enum(['user', 'assistant', 'system']),
  parts: z.array(z.any()),
  createdAt: z.number().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const chatConfigSchema = z.object({
  id: z.string().optional(),
  api: z.string().optional(),
  headers: z.record(z.string(), z.string()).optional(),
  body: z.record(z.string(), z.any()).optional(),
  maxRetries: z.number().min(0).max(10).optional(),
  retryDelay: z.number().min(0).max(10000).optional(),
});

// ============================================================================
// Export all types
// ============================================================================

export type {
  UIMessage,
  UIDataTypes,
  InferUITools,
  ToolSet,
} from 'ai';
