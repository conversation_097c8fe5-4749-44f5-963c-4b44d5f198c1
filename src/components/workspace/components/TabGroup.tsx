'use client';

import React, { useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Plus, 
  ChevronLeft, 
  ChevronRight, 
  MoreHorizontal,
  Split,
  Circle
} from 'lucide-react';
import { TabGroupConfig, TabConfig } from '../types';
import { useWorkspace } from '../context/WorkspaceContext';
import { useDragDrop } from '../context/DragDropContext';

interface TabGroupProps {
  config: TabGroupConfig;
  layoutId: string;
  className?: string;
}

export function TabGroup({ config, layoutId, className }: TabGroupProps) {
  const { setActiveTab, removeTab, splitTabGroup } = useWorkspace();
  const { setDraggedItem } = useDragDrop();
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const tabsRef = useRef<HTMLDivElement>(null);

  const activeTab = config.tabs.find(tab => tab.id === config.activeTabId);

  const handleTabClick = (tabId: string) => {
    setActiveTab(layoutId, config.id, tabId);
  };

  const handleTabClose = (tabId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    removeTab(layoutId, config.id, tabId);
  };

  const handleTabDragStart = (tab: TabConfig, e: React.DragEvent) => {
    setDraggedItem({
      type: 'tab',
      id: tab.id,
      groupId: config.id,
      layoutId,
    });
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleSplit = (direction: 'horizontal' | 'vertical') => {
    if (config.allowSplit) {
      splitTabGroup(layoutId, config.id, direction);
    }
  };

  const scrollTabs = (direction: 'left' | 'right') => {
    if (tabsRef.current) {
      const scrollAmount = 200;
      const currentScroll = tabsRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      tabsRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className={cn("h-full flex flex-col bg-background", className)}>
      {/* Tab Bar */}
      <div className="flex items-center border-b bg-muted/30">
        {showScrollButtons && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 shrink-0"
            onClick={() => scrollTabs('left')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}
        
        <div 
          ref={tabsRef}
          className="flex-1 flex overflow-x-auto scrollbar-hide"
          onScroll={() => {
            if (tabsRef.current) {
              const { scrollLeft, scrollWidth, clientWidth } = tabsRef.current;
              setShowScrollButtons(scrollWidth > clientWidth);
            }
          }}
        >
          {config.tabs.map((tab) => (
            <Tab
              key={tab.id}
              tab={tab}
              isActive={tab.id === config.activeTabId}
              onClick={() => handleTabClick(tab.id)}
              onClose={(e) => handleTabClose(tab.id, e)}
              onDragStart={(e) => handleTabDragStart(tab, e)}
              allowReorder={config.allowReorder}
            />
          ))}
        </div>

        {showScrollButtons && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 shrink-0"
            onClick={() => scrollTabs('right')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}

        <div className="flex items-center gap-1 px-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => {/* Add new tab */}}
          >
            <Plus className="h-3 w-3" />
          </Button>
          
          {config.allowSplit && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleSplit('horizontal')}
                title="Split horizontally"
              >
                <Split className="h-3 w-3 rotate-90" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleSplit('vertical')}
                title="Split vertically"
              >
                <Split className="h-3 w-3" />
              </Button>
            </>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
          >
            <MoreHorizontal className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab ? (
          <TabContent tab={activeTab} />
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <h3 className="font-medium mb-2">No tabs open</h3>
              <p className="text-sm">Open a file or create a new tab to get started</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

interface TabProps {
  tab: TabConfig;
  isActive: boolean;
  onClick: () => void;
  onClose: (e: React.MouseEvent) => void;
  onDragStart: (e: React.DragEvent) => void;
  allowReorder?: boolean;
}

function Tab({ tab, isActive, onClick, onClose, onDragStart, allowReorder }: TabProps) {
  return (
    <div
      className={cn(
        "flex items-center gap-2 px-3 py-2 border-r cursor-pointer select-none min-w-0 max-w-[200px] group",
        isActive 
          ? "bg-background border-b-2 border-b-primary" 
          : "bg-muted/50 hover:bg-muted"
      )}
      onClick={onClick}
      draggable={allowReorder}
      onDragStart={onDragStart}
    >
      {tab.icon && <tab.icon className="h-4 w-4 shrink-0" />}
      
      <span className="truncate text-sm font-medium flex-1">
        {tab.title}
      </span>
      
      {tab.isDirty && (
        <Circle className="h-2 w-2 fill-current shrink-0" />
      )}
      
      {tab.closable && (
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive hover:text-destructive-foreground shrink-0"
          onClick={onClose}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}

function TabContent({ tab }: { tab: TabConfig }) {
  const ContentComponent = tab.content;
  
  return (
    <div className="h-full w-full">
      <ContentComponent {...(tab.props || {})} />
    </div>
  );
}
