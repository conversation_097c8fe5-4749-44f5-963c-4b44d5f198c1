'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { GitBranch, GitCommit, GitPullRequest, Plus, MoreHorizontal } from 'lucide-react';
import { PanelConfig } from '../../types';

interface GitPanelProps {
  config: PanelConfig;
}

export function GitPanel({ config }: GitPanelProps) {
  return (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium">Source Control</span>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <Plus className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <GitBranch className="h-4 w-4" />
          <span>main</span>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-3">
          <div className="text-center text-muted-foreground py-8">
            <GitCommit className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">No changes detected</p>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
