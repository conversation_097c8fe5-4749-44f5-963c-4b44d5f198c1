'use client';

import React from 'react';
import { PanelConfig } from '../../types';
import { AdvancedChat } from '@/components/ai/advanced-chat';

interface AIChatPanelProps {
  config: PanelConfig;
}

export function AIChatPanel({ config }: AIChatPanelProps) {
  return (
    <div className="h-full">
      <AdvancedChat
        id="workspace-ai-panel"
        api="/api/workspace-chat"
        title="AI Assistant"
        subtitle="Workspace AI Helper"
        placeholder="Ask me anything about your workspace..."
        showTimestamps={true}
        showTokenCount={false}
        allowFileUpload={true}
        allowRegenerate={true}
        allowDelete={true}
        allowCopy={true}
        allowExport={false}
        className="h-full border-none"
      />
    </div>
  );
}
