'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AlertTriangle, AlertCircle, Info, MoreHorizontal } from 'lucide-react';
import { PanelConfig } from '../../types';

interface ProblemsPanelProps {
  config: PanelConfig;
}

interface Problem {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  file: string;
  line: number;
  column: number;
}

export function ProblemsPanel({ config }: ProblemsPanelProps) {
  const [problems] = useState<Problem[]>([
    {
      id: '1',
      type: 'error',
      message: 'Cannot find module \'zustand\' or its corresponding type declarations.',
      file: 'src/components/workspace/context/WorkspaceContext.tsx',
      line: 3,
      column: 1,
    },
    {
      id: '2',
      type: 'warning',
      message: '\'position\' is declared but its value is never read.',
      file: 'src/components/workspace/context/WorkspaceContext.tsx',
      line: 115,
      column: 15,
    },
  ]);

  const getIcon = (type: Problem['type']) => {
    switch (type) {
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getTypeColor = (type: Problem['type']) => {
    switch (type) {
      case 'error':
        return 'text-red-500';
      case 'warning':
        return 'text-yellow-500';
      case 'info':
        return 'text-blue-500';
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium">Problems</span>
            <div className="flex items-center gap-3 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <AlertCircle className="h-3 w-3 text-red-500" />
                {problems.filter(p => p.type === 'error').length}
              </span>
              <span className="flex items-center gap-1">
                <AlertTriangle className="h-3 w-3 text-yellow-500" />
                {problems.filter(p => p.type === 'warning').length}
              </span>
              <span className="flex items-center gap-1">
                <Info className="h-3 w-3 text-blue-500" />
                {problems.filter(p => p.type === 'info').length}
              </span>
            </div>
          </div>
          
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <MoreHorizontal className="h-3 w-3" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="divide-y">
          {problems.map(problem => (
            <div
              key={problem.id}
              className="p-3 hover:bg-muted/50 cursor-pointer"
            >
              <div className="flex items-start gap-2">
                {getIcon(problem.type)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-foreground mb-1">
                    {problem.message}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {problem.file}:{problem.line}:{problem.column}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
