'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Play, Square, RotateCcw, Bug, MoreHorizontal } from 'lucide-react';
import { PanelConfig } from '../../types';

interface DebugPanelProps {
  config: PanelConfig;
}

export function DebugPanel({ config }: DebugPanelProps) {
  return (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium">Debug</span>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <MoreHorizontal className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="sm" className="h-7 px-2">
            <Play className="h-3 w-3 mr-1" />
            Start
          </Button>
          <Button variant="ghost" size="sm" className="h-7 px-2">
            <Square className="h-3 w-3 mr-1" />
            Stop
          </Button>
          <Button variant="ghost" size="sm" className="h-7 px-2">
            <RotateCcw className="h-3 w-3 mr-1" />
            Restart
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-3">
          <div className="text-center text-muted-foreground py-8">
            <Bug className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">No debug session active</p>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
