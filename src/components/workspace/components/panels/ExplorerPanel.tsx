'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Folder, 
  FolderOpen, 
  File, 
  Search,
  Plus,
  MoreHorizontal,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import { PanelConfig } from '../../types';

interface ExplorerPanelProps {
  config: PanelConfig;
}

interface FileTreeNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: FileTreeNode[];
  isExpanded?: boolean;
}

// Mock file tree data
const mockFileTree: FileTreeNode[] = [
  {
    id: '1',
    name: 'src',
    type: 'folder',
    path: '/src',
    isExpanded: true,
    children: [
      {
        id: '2',
        name: 'components',
        type: 'folder',
        path: '/src/components',
        isExpanded: true,
        children: [
          {
            id: '3',
            name: 'workspace',
            type: 'folder',
            path: '/src/components/workspace',
            isExpanded: false,
            children: [
              { id: '4', name: 'WorkspaceLayout.tsx', type: 'file', path: '/src/components/workspace/WorkspaceLayout.tsx' },
              { id: '5', name: 'Panel.tsx', type: 'file', path: '/src/components/workspace/Panel.tsx' },
              { id: '6', name: 'TabGroup.tsx', type: 'file', path: '/src/components/workspace/TabGroup.tsx' },
            ]
          },
          { id: '7', name: 'ui', type: 'folder', path: '/src/components/ui', children: [] },
        ]
      },
      { id: '8', name: 'app', type: 'folder', path: '/src/app', children: [] },
      { id: '9', name: 'lib', type: 'folder', path: '/src/lib', children: [] },
    ]
  },
  { id: '10', name: 'package.json', type: 'file', path: '/package.json' },
  { id: '11', name: 'README.md', type: 'file', path: '/README.md' },
  { id: '12', name: 'tsconfig.json', type: 'file', path: '/tsconfig.json' },
];

export function ExplorerPanel({ config }: ExplorerPanelProps) {
  const [fileTree, setFileTree] = useState<FileTreeNode[]>(mockFileTree);
  const [searchQuery, setSearchQuery] = useState('');

  const toggleFolder = (nodeId: string) => {
    const updateNode = (nodes: FileTreeNode[]): FileTreeNode[] => {
      return nodes.map(node => {
        if (node.id === nodeId && node.type === 'folder') {
          return { ...node, isExpanded: !node.isExpanded };
        }
        if (node.children) {
          return { ...node, children: updateNode(node.children) };
        }
        return node;
      });
    };
    
    setFileTree(updateNode(fileTree));
  };

  const handleFileClick = (node: FileTreeNode) => {
    if (node.type === 'file') {
      // TODO: Open file in editor
      console.log('Opening file:', node.path);
    } else {
      toggleFolder(node.id);
    }
  };

  const filteredTree = searchQuery 
    ? filterTree(fileTree, searchQuery.toLowerCase())
    : fileTree;

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-3 border-b">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium">Explorer</span>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <Plus className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-7 h-7 text-xs"
          />
        </div>
      </div>

      {/* File Tree */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {filteredTree.map(node => (
            <FileTreeItem
              key={node.id}
              node={node}
              level={0}
              onToggle={toggleFolder}
              onClick={handleFileClick}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface FileTreeItemProps {
  node: FileTreeNode;
  level: number;
  onToggle: (nodeId: string) => void;
  onClick: (node: FileTreeNode) => void;
}

function FileTreeItem({ node, level, onToggle, onClick }: FileTreeItemProps) {
  const paddingLeft = level * 12 + 8;

  return (
    <div>
      <div
        className={cn(
          "flex items-center gap-1 py-1 px-2 rounded cursor-pointer hover:bg-muted/50 text-sm",
          "select-none"
        )}
        style={{ paddingLeft }}
        onClick={() => onClick(node)}
      >
        {node.type === 'folder' ? (
          <>
            {node.isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            {node.isExpanded ? (
              <FolderOpen className="h-4 w-4 text-blue-500" />
            ) : (
              <Folder className="h-4 w-4 text-blue-500" />
            )}
          </>
        ) : (
          <>
            <div className="w-3" /> {/* Spacer for alignment */}
            <File className="h-4 w-4 text-muted-foreground" />
          </>
        )}
        
        <span className="truncate">{node.name}</span>
      </div>

      {node.type === 'folder' && node.isExpanded && node.children && (
        <div>
          {node.children.map(child => (
            <FileTreeItem
              key={child.id}
              node={child}
              level={level + 1}
              onToggle={onToggle}
              onClick={onClick}
            />
          ))}
        </div>
      )}
    </div>
  );
}

function filterTree(nodes: FileTreeNode[], query: string): FileTreeNode[] {
  return nodes.reduce<FileTreeNode[]>((acc, node) => {
    const matchesQuery = node.name.toLowerCase().includes(query);
    const filteredChildren = node.children ? filterTree(node.children, query) : [];
    
    if (matchesQuery || filteredChildren.length > 0) {
      acc.push({
        ...node,
        children: filteredChildren,
        isExpanded: filteredChildren.length > 0 ? true : node.isExpanded,
      });
    }
    
    return acc;
  }, []);
}
