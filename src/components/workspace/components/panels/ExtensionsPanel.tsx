'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Package, Search, MoreHorizontal } from 'lucide-react';
import { PanelConfig } from '../../types';

interface ExtensionsPanelProps {
  config: PanelConfig;
}

export function ExtensionsPanel({ config }: ExtensionsPanelProps) {
  return (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium">Extensions</span>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <MoreHorizontal className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
          <Input
            placeholder="Search extensions..."
            className="pl-7 h-8 text-sm"
          />
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-3">
          <div className="text-center text-muted-foreground py-8">
            <Package className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">No extensions installed</p>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
