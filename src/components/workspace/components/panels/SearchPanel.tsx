'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Search, Replace, FileText, MoreHorizontal } from 'lucide-react';
import { PanelConfig } from '../../types';

interface SearchPanelProps {
  config: PanelConfig;
}

export function SearchPanel({ config }: SearchPanelProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [replaceQuery, setReplaceQuery] = useState('');
  const [showReplace, setShowReplace] = useState(false);

  return (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Search</span>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setShowReplace(!showReplace)}
          >
            <Replace className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
            <Input
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-7 h-8 text-sm"
            />
          </div>
          
          {showReplace && (
            <div className="relative">
              <Replace className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
              <Input
                placeholder="Replace..."
                value={replaceQuery}
                onChange={(e) => setReplaceQuery(e.target.value)}
                className="pl-7 h-8 text-sm"
              />
            </div>
          )}
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-3">
          <div className="text-center text-muted-foreground py-8">
            <Search className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">Enter search terms to find matches</p>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
