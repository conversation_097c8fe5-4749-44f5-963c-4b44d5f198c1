'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Terminal, 
  Plus, 
  X, 
  MoreHorizontal,
  Trash2,
  Copy
} from 'lucide-react';
import { PanelConfig } from '../../types';

interface TerminalPanelProps {
  config: PanelConfig;
}

interface TerminalSession {
  id: string;
  name: string;
  history: TerminalLine[];
  currentDirectory: string;
  isActive: boolean;
}

interface TerminalLine {
  id: string;
  type: 'command' | 'output' | 'error';
  content: string;
  timestamp: Date;
}

export function TerminalPanel({ config }: TerminalPanelProps) {
  const [sessions, setSessions] = useState<TerminalSession[]>([
    {
      id: '1',
      name: 'Terminal 1',
      history: [
        {
          id: '1',
          type: 'output',
          content: 'Welcome to Omnispace Terminal',
          timestamp: new Date(),
        },
        {
          id: '2',
          type: 'command',
          content: 'ls -la',
          timestamp: new Date(),
        },
        {
          id: '3',
          type: 'output',
          content: `total 24
drwxr-xr-x  6 <USER> <GROUP> 4096 Jan 29 10:30 .
drwxr-xr-x  3 <USER> <GROUP> 4096 Jan 29 10:25 ..
-rw-r--r--  1 <USER> <GROUP>  123 Jan 29 10:30 package.json
drwxr-xr-x  2 <USER> <GROUP> 4096 Jan 29 10:30 src
-rw-r--r--  1 <USER> <GROUP>  456 Jan 29 10:30 README.md`,
          timestamp: new Date(),
        },
      ],
      currentDirectory: '/workspace',
      isActive: true,
    },
  ]);
  
  const [currentInput, setCurrentInput] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  const activeSession = sessions.find(s => s.isActive);

  useEffect(() => {
    // Auto-scroll to bottom when new content is added
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [activeSession?.history]);

  const createNewSession = () => {
    const newSession: TerminalSession = {
      id: Date.now().toString(),
      name: `Terminal ${sessions.length + 1}`,
      history: [
        {
          id: Date.now().toString(),
          type: 'output',
          content: 'New terminal session started',
          timestamp: new Date(),
        },
      ],
      currentDirectory: '/workspace',
      isActive: true,
    };

    setSessions(prev => [
      ...prev.map(s => ({ ...s, isActive: false })),
      newSession,
    ]);
  };

  const closeSession = (sessionId: string) => {
    setSessions(prev => {
      const filtered = prev.filter(s => s.id !== sessionId);
      if (filtered.length > 0 && !filtered.some(s => s.isActive)) {
        filtered[0].isActive = true;
      }
      return filtered;
    });
  };

  const switchSession = (sessionId: string) => {
    setSessions(prev =>
      prev.map(s => ({ ...s, isActive: s.id === sessionId }))
    );
  };

  const executeCommand = (command: string) => {
    if (!activeSession || !command.trim()) return;

    const commandLine: TerminalLine = {
      id: Date.now().toString(),
      type: 'command',
      content: command,
      timestamp: new Date(),
    };

    // Simulate command execution
    const outputLine: TerminalLine = {
      id: (Date.now() + 1).toString(),
      type: 'output',
      content: simulateCommandOutput(command),
      timestamp: new Date(),
    };

    setSessions(prev =>
      prev.map(s =>
        s.id === activeSession.id
          ? {
              ...s,
              history: [...s.history, commandLine, outputLine],
            }
          : s
      )
    );

    setCurrentInput('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      executeCommand(currentInput);
    }
  };

  const clearTerminal = () => {
    if (!activeSession) return;
    
    setSessions(prev =>
      prev.map(s =>
        s.id === activeSession.id
          ? { ...s, history: [] }
          : s
      )
    );
  };

  return (
    <div className="h-full flex flex-col bg-black text-green-400 font-mono">
      {/* Terminal Tabs */}
      <div className="flex items-center bg-gray-900 border-b border-gray-700">
        <div className="flex-1 flex overflow-x-auto">
          {sessions.map(session => (
            <div
              key={session.id}
              className={cn(
                "flex items-center gap-2 px-3 py-2 border-r border-gray-700 cursor-pointer text-xs",
                session.isActive 
                  ? "bg-black text-green-400" 
                  : "bg-gray-800 text-gray-400 hover:bg-gray-700"
              )}
              onClick={() => switchSession(session.id)}
            >
              <Terminal className="h-3 w-3" />
              <span>{session.name}</span>
              {sessions.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    closeSession(session.id);
                  }}
                >
                  <X className="h-2 w-2" />
                </Button>
              )}
            </div>
          ))}
        </div>
        
        <div className="flex items-center gap-1 px-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-gray-400 hover:text-white"
            onClick={createNewSession}
          >
            <Plus className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-gray-400 hover:text-white"
            onClick={clearTerminal}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-gray-400 hover:text-white"
          >
            <MoreHorizontal className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Terminal Content */}
      {activeSession ? (
        <div className="flex-1 flex flex-col">
          <div 
            ref={scrollRef}
            className="flex-1 overflow-y-auto p-2 space-y-1"
          >
            {activeSession.history.map(line => (
              <TerminalLine key={line.id} line={line} />
            ))}
          </div>
          
          {/* Input Line */}
          <div className="flex items-center p-2 border-t border-gray-800">
            <span className="text-blue-400 mr-2">
              {activeSession.currentDirectory} $
            </span>
            <Input
              ref={inputRef}
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1 bg-transparent border-none text-green-400 focus:ring-0 focus:outline-none p-0 h-auto"
              placeholder="Enter command..."
              autoFocus
            />
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <Terminal className="h-8 w-8 mx-auto mb-2" />
            <p>No terminal sessions</p>
            <Button
              variant="ghost"
              size="sm"
              className="mt-2 text-green-400"
              onClick={createNewSession}
            >
              Create New Terminal
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

function TerminalLine({ line }: { line: TerminalLine }) {
  const getLineColor = () => {
    switch (line.type) {
      case 'command':
        return 'text-yellow-400';
      case 'error':
        return 'text-red-400';
      case 'output':
      default:
        return 'text-green-400';
    }
  };

  const getPrefix = () => {
    switch (line.type) {
      case 'command':
        return '$ ';
      case 'error':
        return '✗ ';
      default:
        return '';
    }
  };

  return (
    <div className={cn("text-xs whitespace-pre-wrap", getLineColor())}>
      {getPrefix()}{line.content}
    </div>
  );
}

function simulateCommandOutput(command: string): string {
  const cmd = command.trim().toLowerCase();
  
  if (cmd === 'ls' || cmd === 'ls -la') {
    return `package.json
src/
README.md
tsconfig.json
node_modules/`;
  }
  
  if (cmd === 'pwd') {
    return '/workspace';
  }
  
  if (cmd.startsWith('echo ')) {
    return command.substring(5);
  }
  
  if (cmd === 'date') {
    return new Date().toString();
  }
  
  if (cmd === 'whoami') {
    return 'omnispace-user';
  }
  
  if (cmd === 'clear') {
    return '';
  }
  
  // Default response for unknown commands
  return `Command '${command}' executed successfully`;
}
