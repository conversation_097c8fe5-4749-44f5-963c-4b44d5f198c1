'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { X, Maximize2, Minimize2, MoreH<PERSON>zontal } from 'lucide-react';
import { PanelConfig } from '../types';
import { useWorkspace } from '../context/WorkspaceContext';

// Panel content components
import { ExplorerPanel } from './panels/ExplorerPanel';
import { TerminalPanel } from './panels/TerminalPanel';
import { AIChatPanel } from './panels/AIChatPanel';
import { SearchPanel } from './panels/SearchPanel';
import { GitPanel } from './panels/GitPanel';
import { DebugPanel } from './panels/DebugPanel';
import { ExtensionsPanel } from './panels/ExtensionsPanel';
import { OutputPanel } from './panels/OutputPanel';
import { ProblemsPanel } from './panels/ProblemsPanel';

interface PanelProps {
  config: PanelConfig;
  className?: string;
  isMaximized?: boolean;
  onMaximize?: () => void;
  onMinimize?: () => void;
  onClose?: () => void;
}

export function Panel({ 
  config, 
  className, 
  isMaximized = false,
  onMaximize,
  onMinimize,
  onClose 
}: PanelProps) {
  const { activeLayout, removePanel } = useWorkspace();

  const handleClose = () => {
    if (activeLayout && config.closable) {
      removePanel(activeLayout.id, config.id);
    }
    onClose?.();
  };

  const handleMaximize = () => {
    onMaximize?.();
  };

  const handleMinimize = () => {
    onMinimize?.();
  };

  const PanelContent = getPanelContent(config.type);

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-3 py-2 border-b">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          {config.icon && <config.icon className="h-4 w-4" />}
          {config.title}
        </CardTitle>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={handleMaximize}
          >
            {isMaximized ? (
              <Minimize2 className="h-3 w-3" />
            ) : (
              <Maximize2 className="h-3 w-3" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
          >
            <MoreHorizontal className="h-3 w-3" />
          </Button>
          
          {config.closable && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
              onClick={handleClose}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 p-0 overflow-hidden">
        <PanelContent config={config} />
      </CardContent>
    </Card>
  );
}

function getPanelContent(type: PanelConfig['type']) {
  switch (type) {
    case 'explorer':
      return ExplorerPanel;
    case 'terminal':
      return TerminalPanel;
    case 'ai-chat':
      return AIChatPanel;
    case 'search':
      return SearchPanel;
    case 'git':
      return GitPanel;
    case 'debug':
      return DebugPanel;
    case 'extensions':
      return ExtensionsPanel;
    case 'output':
      return OutputPanel;
    case 'problems':
      return ProblemsPanel;
    case 'editor':
    case 'custom':
    default:
      return DefaultPanel;
  }
}

function DefaultPanel({ config }: { config: PanelConfig }) {
  const ContentComponent = config.content;
  
  if (ContentComponent) {
    return <ContentComponent {...(config.props || {})} />;
  }

  return (
    <div className="flex items-center justify-center h-full text-muted-foreground">
      <div className="text-center">
        <h3 className="font-medium mb-2">{config.title}</h3>
        <p className="text-sm">Panel content not configured</p>
      </div>
    </div>
  );
}
