'use client';

import { create } from 'zustand';
import { DragDropContextType } from '../types';

interface DragDropStore extends DragDropContextType {
  clearDraggedItem: () => void;
}

export const useDragDropStore = create<DragDropStore>((set) => ({
  draggedItem: null,
  dropZones: [],

  setDraggedItem: (item) => {
    set({ draggedItem: item });
  },

  clearDraggedItem: () => {
    set({ draggedItem: null });
  },

  registerDropZone: (zone) => {
    set((state) => ({
      dropZones: [...state.dropZones.filter(z => z.id !== zone.id), zone]
    }));
  },

  unregisterDropZone: (zoneId) => {
    set((state) => ({
      dropZones: state.dropZones.filter(z => z.id !== zoneId)
    }));
  },
}));

// Convenience hook that matches the old API
export const useDragDrop = useDragDropStore;

// Provider component is no longer needed with Zustand, but keeping for compatibility
export function DragDropProvider({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}
