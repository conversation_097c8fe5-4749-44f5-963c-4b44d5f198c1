'use client';

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  WorkspaceState,
  WorkspaceLayoutConfig,
  PanelConfig,
  TabConfig,
  TabGroupConfig,
  WorkspacePreferences
} from '../types';

// Initial state
const initialState: WorkspaceState = {
  layouts: [],
  activeLayoutId: '',
  preferences: {
    autoSave: true,
    tabSize: 2,
    wordWrap: true,
    minimap: true,
    lineNumbers: true,
    theme: 'dark',
    fontSize: 14,
    fontFamily: 'JetBrains Mono, monospace',
  },
};

// Zustand store interface
interface WorkspaceStore extends WorkspaceState {
  // Layout management
  createLayout: (config: Omit<WorkspaceLayoutConfig, 'id'>) => void;
  updateLayout: (id: string, updates: Partial<WorkspaceLayoutConfig>) => void;
  deleteLayout: (id: string) => void;
  setActiveLayout: (id: string) => void;

  // Panel management
  addPanel: (layoutId: string, panel: PanelConfig, position?: string) => void;
  removePanel: (layoutId: string, panelId: string) => void;
  updatePanel: (layoutId: string, panelId: string, updates: Partial<PanelConfig>) => void;

  // Tab management
  addTab: (layoutId: string, groupId: string, tab: TabConfig, index?: number) => void;
  removeTab: (layoutId: string, groupId: string, tabId: string) => void;
  updateTab: (layoutId: string, groupId: string, tabId: string, updates: Partial<TabConfig>) => void;
  setActiveTab: (layoutId: string, groupId: string, tabId: string) => void;
  moveTab: (layoutId: string, fromGroupId: string, toGroupId: string, tabId: string, index?: number) => void;

  // Tab group management
  addTabGroup: (layoutId: string, group: TabGroupConfig, position?: string) => void;
  removeTabGroup: (layoutId: string, groupId: string) => void;
  splitTabGroup: (layoutId: string, groupId: string, direction: 'horizontal' | 'vertical') => void;

  // Preferences
  updatePreferences: (updates: Partial<WorkspacePreferences>) => void;

  // Persistence
  saveLayout: (layoutId: string) => void;
  loadLayout: (layoutId: string) => void;
  exportLayout: (layoutId: string) => string;
  importLayout: (data: string) => void;

  // Computed values
  activeLayout: WorkspaceLayoutConfig | null;
}

// Create Zustand store with persistence and immer
export const useWorkspaceStore = create<WorkspaceStore>()(
  persist(
    immer((set, get) => ({
      ...initialState,

      // Computed values
      get activeLayout() {
        const state = get();
        return state.layouts.find(layout => layout.id === state.activeLayoutId) || null;
      },

      // Layout management
      createLayout: (config) => {
        const id = `layout-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        set((state) => {
          state.layouts.push({ ...config, id });
          state.activeLayoutId = id;
        });
      },

      updateLayout: (id, updates) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === id);
          if (layout) {
            Object.assign(layout, updates);
          }
        });
      },

      deleteLayout: (id) => {
        set((state) => {
          state.layouts = state.layouts.filter(layout => layout.id !== id);
          if (state.activeLayoutId === id) {
            state.activeLayoutId = state.layouts[0]?.id || '';
          }
        });
      },

      setActiveLayout: (id) => {
        set((state) => {
          state.activeLayoutId = id;
        });
      },

      // Panel management
      addPanel: (layoutId, panel, position) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            layout.panels.push(panel);
          }
        });
      },

      removePanel: (layoutId, panelId) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            layout.panels = layout.panels.filter(panel => panel.id !== panelId);
          }
        });
      },

      updatePanel: (layoutId, panelId, updates) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            const panel = layout.panels.find(p => p.id === panelId);
            if (panel) {
              Object.assign(panel, updates);
            }
          }
        });
      },

      // Tab management
      addTab: (layoutId, groupId, tab, index) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            const group = layout.tabGroups.find(g => g.id === groupId);
            if (group) {
              if (index !== undefined) {
                group.tabs.splice(index, 0, tab);
              } else {
                group.tabs.push(tab);
              }
              group.activeTabId = tab.id;
            }
          }
        });
      },

      removeTab: (layoutId, groupId, tabId) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            const group = layout.tabGroups.find(g => g.id === groupId);
            if (group) {
              group.tabs = group.tabs.filter(tab => tab.id !== tabId);
              if (group.activeTabId === tabId) {
                group.activeTabId = group.tabs[0]?.id || '';
              }
            }
          }
        });
      },

      updateTab: (layoutId, groupId, tabId, updates) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            const group = layout.tabGroups.find(g => g.id === groupId);
            if (group) {
              const tab = group.tabs.find(t => t.id === tabId);
              if (tab) {
                Object.assign(tab, updates);
              }
            }
          }
        });
      },

      setActiveTab: (layoutId, groupId, tabId) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            const group = layout.tabGroups.find(g => g.id === groupId);
            if (group) {
              group.activeTabId = tabId;
            }
          }
        });
      },

      moveTab: (layoutId, fromGroupId, toGroupId, tabId, index) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            const fromGroup = layout.tabGroups.find(g => g.id === fromGroupId);
            const toGroup = layout.tabGroups.find(g => g.id === toGroupId);

            if (fromGroup && toGroup) {
              const tabIndex = fromGroup.tabs.findIndex(t => t.id === tabId);
              if (tabIndex !== -1) {
                const [tab] = fromGroup.tabs.splice(tabIndex, 1);
                if (index !== undefined) {
                  toGroup.tabs.splice(index, 0, tab);
                } else {
                  toGroup.tabs.push(tab);
                }
                toGroup.activeTabId = tab.id;
              }
            }
          }
        });
      },

      // Tab group management
      addTabGroup: (layoutId, group, position) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            layout.tabGroups.push(group);
          }
        });
      },

      removeTabGroup: (layoutId, groupId) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            layout.tabGroups = layout.tabGroups.filter(group => group.id !== groupId);
          }
        });
      },

      splitTabGroup: (layoutId, groupId, direction) => {
        set((state) => {
          const layout = state.layouts.find(l => l.id === layoutId);
          if (layout) {
            const group = layout.tabGroups.find(g => g.id === groupId);
            if (group) {
              const newGroupId = `group-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
              const newGroup: TabGroupConfig = {
                id: newGroupId,
                tabs: [],
                activeTabId: '',
                orientation: direction,
                allowReorder: true,
                allowSplit: true,
              };
              layout.tabGroups.push(newGroup);
            }
          }
        });
      },

      // Preferences
      updatePreferences: (updates) => {
        set((state) => {
          Object.assign(state.preferences, updates);
        });
      },

      // Persistence
      saveLayout: (layoutId) => {
        console.log('Saving layout:', layoutId);
      },

      loadLayout: (layoutId) => {
        console.log('Loading layout:', layoutId);
      },

      exportLayout: (layoutId) => {
        const state = get();
        const layout = state.layouts.find(l => l.id === layoutId);
        return layout ? JSON.stringify(layout, null, 2) : '';
      },

      importLayout: (data) => {
        try {
          const layout = JSON.parse(data);
          get().createLayout(layout);
        } catch (error) {
          console.error('Failed to import layout:', error);
        }
      },
    })),
    {
      name: 'omnispace-workspace-state',
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// Convenience hook that matches the old API
export const useWorkspace = useWorkspaceStore;
