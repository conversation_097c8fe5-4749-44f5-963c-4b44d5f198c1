export interface PanelConfig {
  id: string;
  title: string;
  type: PanelType;
  content?: React.ComponentType<any>;
  props?: Record<string, any>;
  closable?: boolean;
  icon?: React.ComponentType<any>;
  minSize?: number;
  maxSize?: number;
  defaultSize?: number;
}

export interface TabConfig {
  id: string;
  title: string;
  content: React.ComponentType<any>;
  props?: Record<string, any>;
  closable?: boolean;
  icon?: React.ComponentType<any>;
  isDirty?: boolean;
  path?: string;
}

export interface TabGroupConfig {
  id: string;
  tabs: TabConfig[];
  activeTabId?: string;
  orientation?: 'horizontal' | 'vertical';
  allowReorder?: boolean;
  allowSplit?: boolean;
}

export interface WorkspaceLayoutConfig {
  id: string;
  name: string;
  panels: PanelConfig[];
  tabGroups: TabGroupConfig[];
  layout: LayoutNode;
  theme?: WorkspaceTheme;
}

export interface LayoutNode {
  type: 'panel' | 'tabGroup' | 'split';
  id: string;
  direction?: 'horizontal' | 'vertical';
  size?: number;
  minSize?: number;
  maxSize?: number;
  children?: LayoutNode[];
}

export type PanelType = 
  | 'editor' 
  | 'terminal' 
  | 'explorer' 
  | 'search' 
  | 'git' 
  | 'debug' 
  | 'extensions' 
  | 'output' 
  | 'problems' 
  | 'ai-chat'
  | 'custom';

export interface WorkspaceTheme {
  name: string;
  colors: {
    background: string;
    foreground: string;
    border: string;
    accent: string;
    muted: string;
    destructive: string;
  };
  spacing: {
    panel: number;
    tab: number;
    content: number;
  };
}

export interface WorkspaceState {
  layouts: WorkspaceLayoutConfig[];
  activeLayoutId: string;
  preferences: WorkspacePreferences;
}

export interface WorkspacePreferences {
  autoSave: boolean;
  tabSize: number;
  wordWrap: boolean;
  minimap: boolean;
  lineNumbers: boolean;
  theme: string;
  fontSize: number;
  fontFamily: string;
}

// WorkspaceContextType is now handled by Zustand store interface in WorkspaceContext.tsx

export interface DragDropContextType {
  draggedItem: {
    type: 'tab' | 'panel';
    id: string;
    groupId?: string;
    layoutId: string;
  } | null;
  setDraggedItem: (item: DragDropContextType['draggedItem']) => void;
  dropZones: DropZone[];
  registerDropZone: (zone: DropZone) => void;
  unregisterDropZone: (zoneId: string) => void;
}

export interface DropZone {
  id: string;
  type: 'tab-group' | 'panel-container' | 'split-target';
  bounds: DOMRect;
  accepts: ('tab' | 'panel')[];
  onDrop: (item: DragDropContextType['draggedItem']) => void;
}

export interface KeyboardShortcut {
  key: string;
  modifiers: ('ctrl' | 'alt' | 'shift' | 'meta')[];
  action: string;
  description: string;
}

export interface WorkspaceCommand {
  id: string;
  title: string;
  category: string;
  keybinding?: KeyboardShortcut;
  execute: () => void | Promise<void>;
}
