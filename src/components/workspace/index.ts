// Main workspace components
export { WorkspaceLayout, defaultLayouts } from './components/WorkspaceLayout';
export { Panel } from './components/Panel';
export { TabGroup } from './components/TabGroup';

// Panel components
export * from './components/panels';

// Context and stores
export { useWorkspace } from './context/WorkspaceContext';
export { useDragDrop, DragDropProvider } from './context/DragDropContext';

// Types
export * from './types';
