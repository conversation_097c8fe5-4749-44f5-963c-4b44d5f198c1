'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CloudSun, 
  Bell, 
  TrendingUp, 
  Link, 
  Code,
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ExternalLink
} from 'lucide-react';
import { 
  WeatherData, 
  NotificationData, 
  ProgressData, 
  SourceData, 
  CodeData 
} from '@/types/ai-sdk-ui';

interface DataPartRendererProps {
  dataPart: any;
  className?: string;
}

export function DataPartRenderer({ dataPart, className = '' }: DataPartRendererProps) {
  const dataType = dataPart.type.replace('data-', '');
  const data = dataPart.data;

  const renderWeatherData = (weatherData: WeatherData) => {
    return (
      <Card className={`border-l-4 border-l-blue-500 ${className}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <CloudSun className="h-4 w-4 text-blue-500" />
            <CardTitle className="text-sm">Weather Update</CardTitle>
            <Badge variant={weatherData.status === 'success' ? 'default' : 'secondary'}>
              {weatherData.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{weatherData.city}</span>
              {weatherData.temperature && (
                <span className="text-lg font-bold">{weatherData.temperature}°</span>
              )}
            </div>
            {weatherData.weather && (
              <p className="text-sm text-muted-foreground capitalize">
                {weatherData.weather}
              </p>
            )}
            {weatherData.humidity && (
              <div className="text-xs text-muted-foreground">
                Humidity: {weatherData.humidity}%
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderNotificationData = (notificationData: NotificationData) => {
    const getIcon = () => {
      switch (notificationData.level) {
        case 'error':
          return <XCircle className="h-4 w-4" />;
        case 'warning':
          return <AlertTriangle className="h-4 w-4" />;
        case 'success':
          return <CheckCircle className="h-4 w-4" />;
        default:
          return <Info className="h-4 w-4" />;
      }
    };

    const getVariant = () => {
      switch (notificationData.level) {
        case 'error':
          return 'destructive';
        case 'warning':
          return 'default';
        default:
          return 'default';
      }
    };

    return (
      <Alert variant={getVariant()} className={className}>
        <div className="flex items-center gap-2">
          {getIcon()}
          <Bell className="h-4 w-4" />
        </div>
        <AlertDescription className="ml-6">
          <div className="flex items-center justify-between">
            <span>{notificationData.message}</span>
            {notificationData.timestamp && (
              <span className="text-xs opacity-70">
                {new Date(notificationData.timestamp).toLocaleTimeString()}
              </span>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  };

  const renderProgressData = (progressData: ProgressData) => {
    return (
      <Card className={`border-l-4 border-l-green-500 ${className}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <CardTitle className="text-sm">Progress Update</CardTitle>
            <Badge variant="outline">{progressData.percentage}%</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {progressData.message && (
              <p className="text-sm text-muted-foreground">
                {progressData.message}
              </p>
            )}
            <div className="space-y-1">
              <Progress value={progressData.percentage} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{progressData.current} / {progressData.total}</span>
                <span>{progressData.percentage.toFixed(1)}%</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderSourceData = (sourceData: SourceData) => {
    const getTypeIcon = () => {
      switch (sourceData.type) {
        case 'url':
          return <ExternalLink className="h-4 w-4" />;
        case 'document':
          return <Code className="h-4 w-4" />;
        case 'image':
          return <img className="h-4 w-4" />;
        default:
          return <Link className="h-4 w-4" />;
      }
    };

    return (
      <Card className={`border-l-4 border-l-purple-500 ${className}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            {getTypeIcon()}
            <CardTitle className="text-sm">Source Reference</CardTitle>
            <Badge variant="outline" className="capitalize">
              {sourceData.type}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div>
              <h4 className="text-sm font-medium">{sourceData.title}</h4>
              {sourceData.description && (
                <p className="text-xs text-muted-foreground mt-1">
                  {sourceData.description}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <a 
                href={sourceData.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1"
              >
                <ExternalLink className="h-3 w-3" />
                View Source
              </a>
              <span className="text-xs text-muted-foreground">
                ID: {sourceData.id}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderCodeData = (codeData: CodeData) => {
    const getStatusColor = () => {
      switch (codeData.status) {
        case 'complete':
          return 'border-l-green-500';
        case 'error':
          return 'border-l-red-500';
        default:
          return 'border-l-yellow-500';
      }
    };

    return (
      <Card className={`border-l-4 ${getStatusColor()} ${className}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            <CardTitle className="text-sm">Code Block</CardTitle>
            <Badge variant="outline">{codeData.language}</Badge>
            <Badge variant={codeData.status === 'complete' ? 'default' : 'secondary'}>
              {codeData.status}
            </Badge>
          </div>
          {codeData.filename && (
            <p className="text-xs text-muted-foreground mt-1">
              {codeData.filename}
            </p>
          )}
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto">
            <code className={`language-${codeData.language}`}>
              {codeData.code}
            </code>
          </pre>
        </CardContent>
      </Card>
    );
  };

  const renderGenericData = (data: any) => {
    return (
      <Card className={`border-l-4 border-l-gray-500 ${className}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            <CardTitle className="text-sm">Data Update</CardTitle>
            <Badge variant="outline">{dataType}</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        </CardContent>
      </Card>
    );
  };

  // Render based on data type
  switch (dataType) {
    case 'weather':
      return renderWeatherData(data as WeatherData);
    case 'notification':
      return renderNotificationData(data as NotificationData);
    case 'progress':
      return renderProgressData(data as ProgressData);
    case 'source':
      return renderSourceData(data as SourceData);
    case 'code':
      return renderCodeData(data as CodeData);
    default:
      return renderGenericData(data);
  }
}
