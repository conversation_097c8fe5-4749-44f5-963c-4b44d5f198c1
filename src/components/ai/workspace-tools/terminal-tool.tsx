'use client';

import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Terminal, 
  Play, 
  Square, 
  Copy, 
  Download,
  RefreshCw,
  Maximize2,
  Minimize2
} from 'lucide-react';

interface WorkspaceTerminalToolProps {
  toolCall: any;
  onUpdate?: (data: any) => void;
  onError?: (error: Error) => void;
}

interface TerminalLine {
  id: string;
  type: 'command' | 'output' | 'error';
  content: string;
  timestamp: number;
}

export function WorkspaceTerminalTool({ 
  toolCall, 
  onUpdate, 
  onError 
}: WorkspaceTerminalToolProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [currentCommand, setCurrentCommand] = useState('');
  const [terminalLines, setTerminalLines] = useState<TerminalLine[]>([]);
  const [workingDirectory, setWorkingDirectory] = useState('/workspace');
  
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize with tool call data
  useEffect(() => {
    if (toolCall.args?.command) {
      executeCommand(toolCall.args.command);
    }
    if (toolCall.args?.workingDirectory) {
      setWorkingDirectory(toolCall.args.workingDirectory);
    }
  }, [toolCall]);

  // Auto-scroll to bottom when new lines are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [terminalLines]);

  const executeCommand = async (command: string) => {
    if (!command.trim() || isExecuting) return;

    setIsExecuting(true);
    
    // Add command to terminal
    const commandLine: TerminalLine = {
      id: `cmd-${Date.now()}`,
      type: 'command',
      content: `${workingDirectory}$ ${command}`,
      timestamp: Date.now(),
    };
    
    setTerminalLines(prev => [...prev, commandLine]);

    try {
      // Simulate command execution (in real implementation, this would call the workspace API)
      const response = await simulateCommandExecution(command, workingDirectory);
      
      // Add output lines
      const outputLines: TerminalLine[] = response.output.map((line, index) => ({
        id: `out-${Date.now()}-${index}`,
        type: response.exitCode === 0 ? 'output' : 'error',
        content: line,
        timestamp: Date.now() + index,
      }));

      setTerminalLines(prev => [...prev, ...outputLines]);

      // Update working directory if command was cd
      if (command.startsWith('cd ') && response.exitCode === 0) {
        setWorkingDirectory(response.workingDirectory || workingDirectory);
      }

      // Notify parent component
      onUpdate?.({
        command,
        output: response.output,
        exitCode: response.exitCode,
        workingDirectory: response.workingDirectory || workingDirectory,
      });

    } catch (error) {
      const errorLine: TerminalLine = {
        id: `err-${Date.now()}`,
        type: 'error',
        content: `Error: ${error instanceof Error ? error.message : 'Command execution failed'}`,
        timestamp: Date.now(),
      };
      
      setTerminalLines(prev => [...prev, errorLine]);
      onError?.(error instanceof Error ? error : new Error('Command execution failed'));
    } finally {
      setIsExecuting(false);
      setCurrentCommand('');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    executeCommand(currentCommand);
  };

  const clearTerminal = () => {
    setTerminalLines([]);
  };

  const copyOutput = () => {
    const output = terminalLines
      .map(line => line.content)
      .join('\n');
    navigator.clipboard.writeText(output);
  };

  const exportSession = () => {
    const sessionData = {
      workingDirectory,
      lines: terminalLines,
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(sessionData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `terminal-session-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="border-l-4 border-l-green-500">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Terminal className="h-4 w-4 text-green-500" />
            <CardTitle className="text-sm">Workspace Terminal</CardTitle>
            <Badge variant="outline" className="font-mono text-xs">
              {workingDirectory}
            </Badge>
            {isExecuting && (
              <Badge variant="secondary" className="text-xs">
                Executing...
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={copyOutput}
              disabled={terminalLines.length === 0}
            >
              <Copy className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={exportSession}
              disabled={terminalLines.length === 0}
            >
              <Download className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearTerminal}
              disabled={terminalLines.length === 0}
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <Minimize2 className="h-3 w-3" />
              ) : (
                <Maximize2 className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className={`bg-black text-green-400 font-mono text-sm ${
          isExpanded ? 'h-96' : 'h-48'
        }`}>
          <ScrollArea className="h-full p-4" ref={scrollAreaRef}>
            <div className="space-y-1">
              {terminalLines.length === 0 ? (
                <div className="text-gray-500">
                  Terminal ready. Type a command below.
                </div>
              ) : (
                terminalLines.map((line) => (
                  <div
                    key={line.id}
                    className={`${
                      line.type === 'command' 
                        ? 'text-yellow-400 font-semibold' 
                        : line.type === 'error'
                        ? 'text-red-400'
                        : 'text-green-400'
                    }`}
                  >
                    {line.content}
                  </div>
                ))
              )}
              
              {isExecuting && (
                <div className="text-yellow-400 animate-pulse">
                  Executing command...
                </div>
              )}
            </div>
          </ScrollArea>
          
          <div className="border-t border-gray-700 p-4">
            <form onSubmit={handleSubmit} className="flex items-center gap-2">
              <span className="text-yellow-400 font-semibold">
                {workingDirectory}$
              </span>
              <Input
                ref={inputRef}
                value={currentCommand}
                onChange={(e) => setCurrentCommand(e.target.value)}
                placeholder="Enter command..."
                disabled={isExecuting}
                className="flex-1 bg-transparent border-none text-green-400 font-mono focus:ring-0 focus:outline-none"
                autoComplete="off"
              />
              <Button
                type="submit"
                variant="ghost"
                size="sm"
                disabled={!currentCommand.trim() || isExecuting}
                className="text-green-400 hover:text-green-300"
              >
                {isExecuting ? (
                  <Square className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>
            </form>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Simulate command execution (replace with actual API call)
async function simulateCommandExecution(command: string, workingDirectory: string) {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

  // Simple command simulation
  if (command === 'ls' || command === 'ls -la') {
    return {
      output: [
        'total 24',
        'drwxr-xr-x  4 <USER> <GROUP> 4096 Jan 29 10:30 .',
        'drwxr-xr-x  3 <USER> <GROUP> 4096 Jan 29 10:00 ..',
        '-rw-r--r--  1 <USER> <GROUP>  220 Jan 29 10:00 .bashrc',
        'drwxr-xr-x  2 <USER> <GROUP> 4096 Jan 29 10:30 documents',
        '-rw-r--r--  1 <USER> <GROUP> 1024 Jan 29 10:15 example.txt',
        'drwxr-xr-x  3 <USER> <GROUP> 4096 Jan 29 10:20 projects',
      ],
      exitCode: 0,
      workingDirectory,
    };
  }

  if (command === 'pwd') {
    return {
      output: [workingDirectory],
      exitCode: 0,
      workingDirectory,
    };
  }

  if (command.startsWith('cd ')) {
    const newDir = command.substring(3).trim();
    const newPath = newDir.startsWith('/') ? newDir : `${workingDirectory}/${newDir}`;
    return {
      output: [],
      exitCode: 0,
      workingDirectory: newPath,
    };
  }

  if (command === 'whoami') {
    return {
      output: ['user'],
      exitCode: 0,
      workingDirectory,
    };
  }

  if (command.startsWith('echo ')) {
    return {
      output: [command.substring(5)],
      exitCode: 0,
      workingDirectory,
    };
  }

  if (command === 'date') {
    return {
      output: [new Date().toString()],
      exitCode: 0,
      workingDirectory,
    };
  }

  // Default response for unknown commands
  return {
    output: [`bash: ${command}: command not found`],
    exitCode: 127,
    workingDirectory,
  };
}
