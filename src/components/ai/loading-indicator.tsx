'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Loader2, 
  <PERSON><PERSON>, 
  Zap, 
  Brain, 
  MessageSquare,
  Wand2,
  <PERSON>rkles
} from 'lucide-react';
import { AIStatus } from '@/types/ai-sdk-ui';

interface LoadingIndicatorProps {
  status: AIStatus;
  message?: string;
  progress?: number;
  showAnimation?: boolean;
  variant?: 'default' | 'minimal' | 'detailed';
  className?: string;
}

export function LoadingIndicator({ 
  status, 
  message, 
  progress,
  showAnimation = true,
  variant = 'default',
  className = '' 
}: LoadingIndicatorProps) {
  const getStatusConfig = (status: AIStatus) => {
    switch (status) {
      case 'submitted':
        return {
          icon: MessageSquare,
          color: 'text-blue-500',
          bgColor: 'bg-blue-50 dark:bg-blue-950/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          defaultMessage: 'Sending your message...',
          badge: 'Submitted'
        };
      case 'streaming':
        return {
          icon: Brain,
          color: 'text-green-500',
          bgColor: 'bg-green-50 dark:bg-green-950/20',
          borderColor: 'border-green-200 dark:border-green-800',
          defaultMessage: 'AI is thinking and responding...',
          badge: 'Streaming'
        };
      case 'error':
        return {
          icon: Zap,
          color: 'text-red-500',
          bgColor: 'bg-red-50 dark:bg-red-950/20',
          borderColor: 'border-red-200 dark:border-red-800',
          defaultMessage: 'An error occurred',
          badge: 'Error'
        };
      default:
        return {
          icon: Bot,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50 dark:bg-gray-950/20',
          borderColor: 'border-gray-200 dark:border-gray-800',
          defaultMessage: 'Ready',
          badge: 'Ready'
        };
    }
  };

  const config = getStatusConfig(status);
  const IconComponent = config.icon;
  const displayMessage = message || config.defaultMessage;

  // Don't show loading indicator for ready state unless explicitly requested
  if (status === 'ready' && !message) {
    return null;
  }

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center gap-2 text-sm text-muted-foreground ${className}`}>
        {showAnimation && status !== 'ready' && status !== 'error' && (
          <Loader2 className="h-4 w-4 animate-spin" />
        )}
        <IconComponent className={`h-4 w-4 ${config.color}`} />
        <span>{displayMessage}</span>
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card className={`${config.bgColor} border ${config.borderColor} ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="relative">
              <IconComponent className={`h-6 w-6 ${config.color}`} />
              {showAnimation && status === 'streaming' && (
                <div className="absolute -inset-1">
                  <div className="w-8 h-8 border-2 border-transparent border-t-current rounded-full animate-spin opacity-30"></div>
                </div>
              )}
            </div>
            
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">{displayMessage}</span>
                <Badge variant="outline" className="text-xs">
                  {config.badge}
                </Badge>
              </div>
              
              {progress !== undefined && (
                <div className="space-y-1">
                  <Progress value={progress} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    {progress.toFixed(0)}% complete
                  </div>
                </div>
              )}
              
              {status === 'streaming' && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Sparkles className="h-3 w-3" />
                  <span>Generating response...</span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <div className={`flex items-center gap-3 p-3 rounded-lg ${config.bgColor} border ${config.borderColor} ${className}`}>
      <div className="relative">
        {showAnimation && (status === 'submitted' || status === 'streaming') ? (
          <div className="flex items-center gap-2">
            <Loader2 className={`h-5 w-5 animate-spin ${config.color}`} />
            <IconComponent className={`h-5 w-5 ${config.color}`} />
          </div>
        ) : (
          <IconComponent className={`h-5 w-5 ${config.color}`} />
        )}
      </div>
      
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{displayMessage}</span>
          <Badge variant="outline" className="text-xs">
            {config.badge}
          </Badge>
        </div>
        
        {status === 'streaming' && (
          <div className="flex items-center gap-1 mt-1 text-xs text-muted-foreground">
            <div className="flex gap-1">
              <div className="w-1 h-1 bg-current rounded-full animate-pulse"></div>
              <div className="w-1 h-1 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-1 h-1 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
            <span className="ml-1">Streaming response</span>
          </div>
        )}
        
        {progress !== undefined && (
          <div className="mt-2 space-y-1">
            <Progress value={progress} className="h-1" />
            <div className="text-xs text-muted-foreground">
              {progress.toFixed(0)}% complete
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Specialized loading indicators for specific use cases
export function ChatLoadingIndicator({ className = '' }: { className?: string }) {
  return (
    <div className={`flex gap-3 ${className}`}>
      <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center flex-shrink-0">
        <Bot className="h-4 w-4 text-secondary-foreground" />
      </div>
      <div className="bg-secondary text-secondary-foreground rounded-lg px-4 py-3 max-w-[80%]">
        <div className="flex items-center gap-2">
          <div className="flex gap-1">
            <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
          <span className="text-sm">AI is typing...</span>
        </div>
      </div>
    </div>
  );
}

export function CompletionLoadingIndicator({ 
  text = '',
  className = '' 
}: { 
  text?: string;
  className?: string;
}) {
  return (
    <div className={`flex items-center gap-2 p-2 bg-muted rounded-md ${className}`}>
      <Wand2 className="h-4 w-4 text-purple-500 animate-pulse" />
      <span className="text-sm text-muted-foreground">
        Generating completion...
      </span>
      {text && (
        <div className="ml-2 text-sm font-mono bg-background px-2 py-1 rounded">
          {text}
          <span className="animate-pulse">|</span>
        </div>
      )}
    </div>
  );
}

export function ObjectGenerationLoadingIndicator({ 
  schema,
  className = '' 
}: { 
  schema?: string;
  className?: string;
}) {
  return (
    <div className={`flex items-center gap-2 p-3 bg-muted rounded-lg ${className}`}>
      <div className="relative">
        <Sparkles className="h-5 w-5 text-yellow-500" />
        <div className="absolute -inset-1">
          <div className="w-7 h-7 border-2 border-transparent border-t-yellow-500 rounded-full animate-spin opacity-50"></div>
        </div>
      </div>
      <div>
        <div className="text-sm font-medium">Generating structured object...</div>
        {schema && (
          <div className="text-xs text-muted-foreground mt-1">
            Schema: {schema}
          </div>
        )}
      </div>
    </div>
  );
}
