'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useCompletion } from '@ai-sdk/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  Wand2, 
  Square, 
  Copy, 
  Download, 
  RefreshCw, 
  Settings,
  FileText,
  Zap,
  Brain,
  Sparkles,
  RotateCcw,
  Play
} from 'lucide-react';
import { CompletionLoadingIndicator } from './loading-indicator';
import { ErrorDisplay } from './error-display';

interface CompletionProps {
  api?: string;
  placeholder?: string;
  maxLength?: number;
  showSettings?: boolean;
  showStats?: boolean;
  allowExport?: boolean;
  className?: string;
  onComplete?: (completion: string, prompt: string) => void;
}

export function Completion({
  api = '/api/completion',
  placeholder = 'Enter your prompt here...',
  maxLength = 2000,
  showSettings = true,
  showStats = true,
  allowExport = true,
  className = '',
  onComplete,
}: CompletionProps) {
  const [prompt, setPrompt] = useState('');
  const [temperature, setTemperature] = useState([0.7]);
  const [maxTokens, setMaxTokens] = useState([500]);
  const [streaming, setStreaming] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const {
    completion,
    input,
    setInput,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    stop,
  } = useCompletion({
    api,
    body: {
      temperature: temperature[0],
      max_tokens: maxTokens[0],
      stream: streaming,
    },
    onFinish: (prompt, completion) => {
      onComplete?.(completion, prompt);
    },
  });

  const handleCustomSubmit = useCallback((customPrompt?: string) => {
    const promptToUse = customPrompt || prompt;
    if (!promptToUse.trim() || isLoading) return;
    
    setInput(promptToUse);
    // Trigger the form submission
    const form = textareaRef.current?.closest('form');
    if (form) {
      const event = new Event('submit', { bubbles: true, cancelable: true });
      form.dispatchEvent(event);
    }
  }, [prompt, isLoading, setInput]);

  const handleCopy = useCallback(() => {
    if (completion) {
      navigator.clipboard.writeText(completion);
    }
  }, [completion]);

  const handleExport = useCallback(() => {
    if (!completion) return;
    
    const exportData = {
      prompt: input,
      completion,
      settings: {
        temperature: temperature[0],
        maxTokens: maxTokens[0],
        streaming,
      },
      generatedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `completion-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [completion, input, temperature, maxTokens, streaming]);

  const getWordCount = (text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const getCharCount = (text: string) => {
    return text.length;
  };

  const presetPrompts = [
    {
      name: 'Blog Post',
      prompt: 'Write a comprehensive blog post about',
      icon: <FileText className="h-4 w-4" />,
    },
    {
      name: 'Email',
      prompt: 'Write a professional email about',
      icon: <Zap className="h-4 w-4" />,
    },
    {
      name: 'Story',
      prompt: 'Write a creative short story about',
      icon: <Brain className="h-4 w-4" />,
    },
    {
      name: 'Summary',
      prompt: 'Summarize the following text:',
      icon: <Sparkles className="h-4 w-4" />,
    },
  ];

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-purple-500" />
            <CardTitle>Text Completion</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {completion && (
              <>
                <Button variant="outline" size="sm" onClick={handleCopy}>
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </Button>
                {allowExport && (
                  <Button variant="outline" size="sm" onClick={handleExport}>
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                )}
              </>
            )}
            {showSettings && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
              >
                <Settings className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Preset Prompts */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Quick Start</label>
          <div className="flex flex-wrap gap-2">
            {presetPrompts.map((preset, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setPrompt(preset.prompt)}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                {preset.icon}
                {preset.name}
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Advanced Settings */}
        {showAdvanced && (
          <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Advanced Settings</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvanced(false)}
              >
                ×
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Temperature: {temperature[0]}
                </label>
                <Slider
                  value={temperature}
                  onValueChange={setTemperature}
                  max={2}
                  min={0}
                  step={0.1}
                  disabled={isLoading}
                />
                <p className="text-xs text-muted-foreground">
                  Higher values make output more creative
                </p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Max Tokens: {maxTokens[0]}
                </label>
                <Slider
                  value={maxTokens}
                  onValueChange={setMaxTokens}
                  max={2000}
                  min={50}
                  step={50}
                  disabled={isLoading}
                />
                <p className="text-xs text-muted-foreground">
                  Maximum length of the completion
                </p>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <label className="text-sm font-medium">Streaming</label>
                <p className="text-xs text-muted-foreground">
                  Show text as it's being generated
                </p>
              </div>
              <Switch
                checked={streaming}
                onCheckedChange={setStreaming}
                disabled={isLoading}
              />
            </div>
          </div>
        )}

        {/* Input Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Prompt</label>
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={handleInputChange}
              placeholder={placeholder}
              disabled={isLoading}
              rows={4}
              maxLength={maxLength}
              className="resize-none"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{getCharCount(input)}/{maxLength} characters</span>
              <span>{getWordCount(input)} words</span>
            </div>
          </div>

          <div className="flex gap-2">
            {isLoading ? (
              <Button type="button" variant="outline" onClick={stop}>
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
            ) : (
              <Button type="submit" disabled={!input.trim()}>
                <Play className="h-4 w-4 mr-2" />
                Generate
              </Button>
            )}
            
            {completion && !isLoading && (
              <Button
                type="button"
                variant="outline"
                onClick={() => handleCustomSubmit(input)}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Regenerate
              </Button>
            )}
          </div>
        </form>

        {/* Loading State */}
        {isLoading && (
          <CompletionLoadingIndicator text={completion} />
        )}

        {/* Error Display */}
        {error && (
          <ErrorDisplay
            error={error}
            onRetry={() => handleCustomSubmit(input)}
            showDetails
          />
        )}

        {/* Completion Output */}
        {completion && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Generated Text</h3>
              <div className="flex items-center gap-2">
                {showStats && (
                  <>
                    <Badge variant="outline" className="text-xs">
                      {getWordCount(completion)} words
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {getCharCount(completion)} chars
                    </Badge>
                  </>
                )}
              </div>
            </div>
            
            <Card>
              <CardContent className="p-4">
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <div className="whitespace-pre-wrap break-words">
                    {completion}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Specialized completion components
export function EmailCompletion({ className = '', ...props }: Omit<CompletionProps, 'placeholder'>) {
  return (
    <Completion
      {...props}
      placeholder="Describe the email you want to write (e.g., 'A follow-up email to a client about project status')"
      className={className}
    />
  );
}

export function BlogPostCompletion({ className = '', ...props }: Omit<CompletionProps, 'placeholder'>) {
  return (
    <Completion
      {...props}
      placeholder="Describe the blog post topic (e.g., 'The future of artificial intelligence in healthcare')"
      maxLength={4000}
      className={className}
    />
  );
}

export function SummaryCompletion({ className = '', ...props }: Omit<CompletionProps, 'placeholder'>) {
  return (
    <Completion
      {...props}
      placeholder="Paste the text you want to summarize here..."
      maxLength={8000}
      className={className}
    />
  );
}

export function CreativeWritingCompletion({ className = '', ...props }: Omit<CompletionProps, 'placeholder'>) {
  return (
    <Completion
      {...props}
      placeholder="Start your creative writing prompt (e.g., 'Write a story about a time traveler who...')"
      maxLength={3000}
      className={className}
    />
  );
}
