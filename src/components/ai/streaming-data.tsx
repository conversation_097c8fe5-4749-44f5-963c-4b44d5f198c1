'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  Pause, 
  Play, 
  Square, 
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';

interface StreamingDataProps {
  endpoint: string;
  autoStart?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  onData?: (data: any) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
  className?: string;
}

interface StreamEvent {
  id: string;
  type: string;
  data: any;
  timestamp: number;
}

export function StreamingData({
  endpoint,
  autoStart = false,
  maxRetries = 3,
  retryDelay = 1000,
  onData,
  onError,
  onComplete,
  className = '',
}: StreamingDataProps) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [events, setEvents] = useState<StreamEvent[]>([]);
  const [error, setError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [bytesReceived, setBytesReceived] = useState(0);
  const [eventsReceived, setEventsReceived] = useState(0);

  const [eventSourceRef, setEventSourceRef] = useState<EventSource | null>(null);

  const startStreaming = useCallback(() => {
    if (isStreaming) return;

    setIsStreaming(true);
    setIsPaused(false);
    setError(null);
    setConnectionStatus('connecting');

    try {
      const eventSource = new EventSource(endpoint);
      setEventSourceRef(eventSource);

      eventSource.onopen = () => {
        setConnectionStatus('connected');
        setRetryCount(0);
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const streamEvent: StreamEvent = {
            id: `event-${Date.now()}-${Math.random()}`,
            type: data.type || 'message',
            data: data,
            timestamp: Date.now(),
          };

          setEvents(prev => [...prev, streamEvent]);
          setEventsReceived(prev => prev + 1);
          setBytesReceived(prev => prev + event.data.length);

          onData?.(data);
        } catch (parseError) {
          console.error('Failed to parse streaming data:', parseError);
        }
      };

      eventSource.onerror = (event) => {
        const error = new Error('Streaming connection error');
        setError(error);
        setConnectionStatus('error');
        onError?.(error);

        if (retryCount < maxRetries) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
            eventSource.close();
            startStreaming();
          }, retryDelay * Math.pow(2, retryCount)); // Exponential backoff
        } else {
          stopStreaming();
        }
      };

      // Handle custom event types
      eventSource.addEventListener('data', (event) => {
        const customEvent = event as MessageEvent;
        try {
          const data = JSON.parse(customEvent.data);
          const streamEvent: StreamEvent = {
            id: `data-${Date.now()}-${Math.random()}`,
            type: 'data',
            data: data,
            timestamp: Date.now(),
          };

          setEvents(prev => [...prev, streamEvent]);
          setEventsReceived(prev => prev + 1);
          setBytesReceived(prev => prev + customEvent.data.length);

          onData?.(data);
        } catch (parseError) {
          console.error('Failed to parse custom event data:', parseError);
        }
      });

      eventSource.addEventListener('complete', () => {
        setConnectionStatus('disconnected');
        setIsStreaming(false);
        onComplete?.();
      });

    } catch (error) {
      const streamError = error instanceof Error ? error : new Error('Failed to start streaming');
      setError(streamError);
      setConnectionStatus('error');
      setIsStreaming(false);
      onError?.(streamError);
    }
  }, [endpoint, isStreaming, retryCount, maxRetries, retryDelay, onData, onError, onComplete]);

  const stopStreaming = useCallback(() => {
    if (eventSourceRef) {
      eventSourceRef.close();
      setEventSourceRef(null);
    }
    setIsStreaming(false);
    setIsPaused(false);
    setConnectionStatus('disconnected');
  }, [eventSourceRef]);

  const pauseStreaming = useCallback(() => {
    setIsPaused(true);
    // Note: EventSource doesn't support pause/resume, so we'll just stop receiving new events
  }, []);

  const resumeStreaming = useCallback(() => {
    setIsPaused(false);
  }, []);

  const clearEvents = useCallback(() => {
    setEvents([]);
    setEventsReceived(0);
    setBytesReceived(0);
  }, []);

  const exportEvents = useCallback(() => {
    const exportData = {
      endpoint,
      events,
      statistics: {
        totalEvents: eventsReceived,
        totalBytes: bytesReceived,
        duration: events.length > 0 ? events[events.length - 1].timestamp - events[0].timestamp : 0,
      },
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `streaming-data-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [endpoint, events, eventsReceived, bytesReceived]);

  // Auto-start effect
  useEffect(() => {
    if (autoStart) {
      startStreaming();
    }

    return () => {
      if (eventSourceRef) {
        eventSourceRef.close();
      }
    };
  }, [autoStart]); // Only run on mount

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef) {
        eventSourceRef.close();
      }
    };
  }, [eventSourceRef]);

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'connecting':
        return <Clock className="h-4 w-4 text-yellow-500 animate-pulse" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'border-green-500';
      case 'connecting':
        return 'border-yellow-500';
      case 'error':
        return 'border-red-500';
      default:
        return 'border-gray-500';
    }
  };

  return (
    <Card className={`${getStatusColor()} border-l-4 ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <CardTitle className="text-lg">Streaming Data</CardTitle>
            <Badge variant="outline" className="capitalize">
              {connectionStatus}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {!isStreaming ? (
              <Button variant="outline" size="sm" onClick={startStreaming}>
                <Play className="h-4 w-4 mr-1" />
                Start
              </Button>
            ) : (
              <>
                {!isPaused ? (
                  <Button variant="outline" size="sm" onClick={pauseStreaming}>
                    <Pause className="h-4 w-4 mr-1" />
                    Pause
                  </Button>
                ) : (
                  <Button variant="outline" size="sm" onClick={resumeStreaming}>
                    <Play className="h-4 w-4 mr-1" />
                    Resume
                  </Button>
                )}
                <Button variant="outline" size="sm" onClick={stopStreaming}>
                  <Square className="h-4 w-4 mr-1" />
                  Stop
                </Button>
              </>
            )}
            <Button variant="outline" size="sm" onClick={exportEvents} disabled={events.length === 0}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>Events: {eventsReceived}</span>
          <span>Bytes: {(bytesReceived / 1024).toFixed(1)} KB</span>
          {retryCount > 0 && (
            <span>Retries: {retryCount}/{maxRetries}</span>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error.message}
              {retryCount < maxRetries && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={startStreaming}
                  className="ml-2"
                >
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Retry
                </Button>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Connection Status */}
        {isStreaming && (
          <div className="flex items-center gap-2 p-2 bg-muted rounded-md">
            <Zap className="h-4 w-4 text-blue-500" />
            <span className="text-sm">
              {isPaused ? 'Streaming paused' : 'Streaming active'}
            </span>
            {connectionStatus === 'connected' && !isPaused && (
              <div className="ml-auto flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs">Live</span>
              </div>
            )}
          </div>
        )}

        {/* Events Display */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Events ({events.length})</h4>
            {events.length > 0 && (
              <Button variant="ghost" size="sm" onClick={clearEvents}>
                Clear
              </Button>
            )}
          </div>

          <ScrollArea className="h-64 w-full border rounded-md">
            <div className="p-4 space-y-2">
              {events.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No events received yet
                </div>
              ) : (
                events.slice(-50).map((event) => ( // Show last 50 events
                  <div
                    key={event.id}
                    className={`p-2 rounded-md text-sm ${
                      isPaused ? 'opacity-50' : ''
                    } ${
                      event.type === 'error' 
                        ? 'bg-red-50 dark:bg-red-950/20' 
                        : 'bg-muted'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <Badge variant="outline" className="text-xs">
                        {event.type}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <pre className="text-xs overflow-x-auto">
                      {JSON.stringify(event.data, null, 2)}
                    </pre>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
}
