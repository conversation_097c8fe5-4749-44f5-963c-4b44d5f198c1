'use client';

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  AlertCircle, 
  RefreshCw, 
  X, 
  ChevronDown, 
  ChevronRight,
  Wifi,
  Server,
  Key,
  Clock,
  Bug
} from 'lucide-react';
import { AIError } from '@/types/ai-sdk-ui';

interface ErrorDisplayProps {
  error: AIError;
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
  className?: string;
}

export function ErrorDisplay({ 
  error, 
  onRetry, 
  onDismiss, 
  showDetails = false,
  className = '' 
}: ErrorDisplayProps) {
  const [showFullDetails, setShowFullDetails] = React.useState(false);

  if (!error) return null;

  const getErrorType = (error: Error) => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network';
    }
    if (message.includes('unauthorized') || message.includes('api key')) {
      return 'auth';
    }
    if (message.includes('rate limit') || message.includes('quota')) {
      return 'rate_limit';
    }
    if (message.includes('timeout')) {
      return 'timeout';
    }
    if (message.includes('server') || message.includes('500')) {
      return 'server';
    }
    return 'unknown';
  };

  const getErrorIcon = (type: string) => {
    switch (type) {
      case 'network':
        return <Wifi className="h-4 w-4" />;
      case 'auth':
        return <Key className="h-4 w-4" />;
      case 'rate_limit':
        return <Clock className="h-4 w-4" />;
      case 'timeout':
        return <Clock className="h-4 w-4" />;
      case 'server':
        return <Server className="h-4 w-4" />;
      default:
        return <Bug className="h-4 w-4" />;
    }
  };

  const getErrorTitle = (type: string) => {
    switch (type) {
      case 'network':
        return 'Network Error';
      case 'auth':
        return 'Authentication Error';
      case 'rate_limit':
        return 'Rate Limit Exceeded';
      case 'timeout':
        return 'Request Timeout';
      case 'server':
        return 'Server Error';
      default:
        return 'Unexpected Error';
    }
  };

  const getErrorDescription = (type: string, error: Error) => {
    switch (type) {
      case 'network':
        return 'Unable to connect to the AI service. Please check your internet connection and try again.';
      case 'auth':
        return 'Authentication failed. Please check your API key configuration.';
      case 'rate_limit':
        return 'You have exceeded the rate limit. Please wait a moment before trying again.';
      case 'timeout':
        return 'The request took too long to complete. Please try again.';
      case 'server':
        return 'The AI service is experiencing issues. Please try again later.';
      default:
        return error.message || 'An unexpected error occurred while processing your request.';
    }
  };

  const getRetryDelay = (type: string) => {
    switch (type) {
      case 'rate_limit':
        return 60; // 1 minute
      case 'server':
        return 30; // 30 seconds
      case 'timeout':
        return 10; // 10 seconds
      default:
        return 5; // 5 seconds
    }
  };

  const errorType = getErrorType(error);
  const errorTitle = getErrorTitle(errorType);
  const errorDescription = getErrorDescription(errorType, error);
  const retryDelay = getRetryDelay(errorType);

  const isRetryable = ['network', 'timeout', 'server', 'rate_limit'].includes(errorType);

  if (showDetails) {
    return (
      <Card className={`border-destructive ${className}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getErrorIcon(errorType)}
              <CardTitle className="text-sm text-destructive">{errorTitle}</CardTitle>
              <Badge variant="destructive" className="text-xs">
                Error
              </Badge>
            </div>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-3">
          <p className="text-sm text-muted-foreground">
            {errorDescription}
          </p>

          {isRetryable && onRetry && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-3 w-3" />
                Retry
              </Button>
              <span className="text-xs text-muted-foreground">
                Recommended wait: {retryDelay}s
              </span>
            </div>
          )}

          <Collapsible open={showFullDetails} onOpenChange={setShowFullDetails}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 text-xs">
                {showFullDetails ? (
                  <>
                    <ChevronDown className="h-3 w-3 mr-1" />
                    Hide Details
                  </>
                ) : (
                  <>
                    <ChevronRight className="h-3 w-3 mr-1" />
                    Show Details
                  </>
                )}
              </Button>
            </CollapsibleTrigger>
            
            <CollapsibleContent className="mt-2">
              <div className="bg-muted p-3 rounded-md">
                <div className="space-y-2 text-xs">
                  <div>
                    <span className="font-medium">Error Type:</span>
                    <span className="ml-2">{errorType}</span>
                  </div>
                  <div>
                    <span className="font-medium">Message:</span>
                    <span className="ml-2">{error.message}</span>
                  </div>
                  {error.name && (
                    <div>
                      <span className="font-medium">Name:</span>
                      <span className="ml-2">{error.name}</span>
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Timestamp:</span>
                    <span className="ml-2">{new Date().toISOString()}</span>
                  </div>
                  {error.stack && (
                    <div>
                      <span className="font-medium">Stack Trace:</span>
                      <pre className="mt-1 p-2 bg-background rounded text-xs overflow-x-auto">
                        {error.stack}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>
    );
  }

  // Simple alert version
  return (
    <Alert variant="destructive" className={className}>
      <div className="flex items-center gap-2">
        {getErrorIcon(errorType)}
        <AlertCircle className="h-4 w-4" />
      </div>
      <div className="ml-6">
        <AlertTitle className="text-sm">{errorTitle}</AlertTitle>
        <AlertDescription className="text-sm mt-1">
          {errorDescription}
        </AlertDescription>
        {isRetryable && onRetry && (
          <div className="flex items-center gap-2 mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="h-7 text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-7 text-xs"
              >
                Dismiss
              </Button>
            )}
          </div>
        )}
      </div>
    </Alert>
  );
}
