'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Wrench, 
  CloudSun, 
  Calculator, 
  Thermometer, 
  ChevronDown, 
  ChevronRight,
  CheckCircle,
  XCircle,
  Loader2,
  Code,
  Database,
  Search,
  FileText,
  Image as ImageIcon,
  Globe
} from 'lucide-react';

interface ToolCallRendererProps {
  toolCall: any;
  isLoading?: boolean;
  error?: string;
  className?: string;
}

export function ToolCallRenderer({ 
  toolCall, 
  isLoading = false, 
  error, 
  className = '' 
}: ToolCallRendererProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toolName = toolCall.type.replace('tool-', '');
  
  const getToolIcon = (name: string) => {
    switch (name) {
      case 'weather':
        return <CloudSun className="h-4 w-4" />;
      case 'calculator':
      case 'convertFahrenheitToCelsius':
        return <Calculator className="h-4 w-4" />;
      case 'temperature':
        return <Thermometer className="h-4 w-4" />;
      case 'search':
        return <Search className="h-4 w-4" />;
      case 'database':
        return <Database className="h-4 w-4" />;
      case 'code':
        return <Code className="h-4 w-4" />;
      case 'file':
        return <FileText className="h-4 w-4" />;
      case 'image':
        return <ImageIcon className="h-4 w-4" />;
      case 'web':
        return <Globe className="h-4 w-4" />;
      default:
        return <Wrench className="h-4 w-4" />;
    }
  };

  const getToolDisplayName = (name: string) => {
    switch (name) {
      case 'weather':
        return 'Weather';
      case 'calculator':
        return 'Calculator';
      case 'convertFahrenheitToCelsius':
        return 'Temperature Converter';
      case 'search':
        return 'Search';
      case 'database':
        return 'Database Query';
      case 'code':
        return 'Code Execution';
      case 'file':
        return 'File Operation';
      case 'image':
        return 'Image Processing';
      case 'web':
        return 'Web Request';
      default:
        return name.charAt(0).toUpperCase() + name.slice(1);
    }
  };

  const getStatusIcon = () => {
    if (isLoading) {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }
    if (error) {
      return <XCircle className="h-4 w-4 text-destructive" />;
    }
    if (toolCall.result) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    return null;
  };

  const renderToolResult = () => {
    if (error) {
      return (
        <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
          <div className="flex items-center gap-2 text-destructive mb-2">
            <XCircle className="h-4 w-4" />
            <span className="font-medium">Tool Error</span>
          </div>
          <p className="text-sm">{error}</p>
        </div>
      );
    }

    if (!toolCall.result) {
      return (
        <div className="p-3 bg-muted rounded-md">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">Executing tool...</span>
          </div>
        </div>
      );
    }

    // Special rendering for specific tool types
    switch (toolName) {
      case 'weather':
        return renderWeatherResult(toolCall.result);
      case 'calculator':
        return renderCalculatorResult(toolCall.result);
      case 'convertFahrenheitToCelsius':
        return renderTemperatureResult(toolCall.result);
      default:
        return renderGenericResult(toolCall.result);
    }
  };

  const renderWeatherResult = (result: any) => {
    return (
      <div className="p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-md">
        <div className="flex items-center gap-2 mb-2">
          <CloudSun className="h-4 w-4 text-blue-600" />
          <span className="font-medium text-blue-900 dark:text-blue-100">
            Weather in {result.location}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <span className="text-muted-foreground">Temperature:</span>
            <span className="ml-1 font-medium">{result.temperature}°F</span>
          </div>
          <div>
            <span className="text-muted-foreground">Conditions:</span>
            <span className="ml-1 font-medium capitalize">{result.conditions}</span>
          </div>
          {result.humidity && (
            <div>
              <span className="text-muted-foreground">Humidity:</span>
              <span className="ml-1 font-medium">{result.humidity}%</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderCalculatorResult = (result: any) => {
    return (
      <div className="p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-md">
        <div className="flex items-center gap-2 mb-2">
          <Calculator className="h-4 w-4 text-green-600" />
          <span className="font-medium text-green-900 dark:text-green-100">Calculation Result</span>
        </div>
        <div className="text-sm">
          <div className="font-mono bg-background p-2 rounded border">
            {result.expression} = <span className="font-bold text-green-600">{result.result}</span>
          </div>
        </div>
      </div>
    );
  };

  const renderTemperatureResult = (result: any) => {
    return (
      <div className="p-3 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-md">
        <div className="flex items-center gap-2 mb-2">
          <Thermometer className="h-4 w-4 text-orange-600" />
          <span className="font-medium text-orange-900 dark:text-orange-100">Temperature Conversion</span>
        </div>
        <div className="text-sm">
          <div className="font-mono bg-background p-2 rounded border">
            {result.fahrenheit}°F = <span className="font-bold text-orange-600">{result.celsius}°C</span>
          </div>
        </div>
      </div>
    );
  };

  const renderGenericResult = (result: any) => {
    return (
      <div className="p-3 bg-muted rounded-md">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span className="font-medium">Tool Result</span>
        </div>
        <pre className="text-xs bg-background p-2 rounded border overflow-x-auto">
          {JSON.stringify(result, null, 2)}
        </pre>
      </div>
    );
  };

  return (
    <Card className={`border-l-4 border-l-blue-500 ${className}`}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="pb-2 cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getToolIcon(toolName)}
                <CardTitle className="text-sm font-medium">
                  {getToolDisplayName(toolName)}
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  Tool Call
                </Badge>
                {getStatusIcon()}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  {isExpanded ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronRight className="h-3 w-3" />
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0">
            {toolCall.args && (
              <div className="mb-3">
                <h4 className="text-sm font-medium mb-2">Input Parameters:</h4>
                <div className="bg-muted p-2 rounded text-xs">
                  <pre>{JSON.stringify(toolCall.args, null, 2)}</pre>
                </div>
              </div>
            )}

            <div>
              <h4 className="text-sm font-medium mb-2">Result:</h4>
              {renderToolResult()}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
