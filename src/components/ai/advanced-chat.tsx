'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useChat } from '@ai-sdk/react';
import { DefaultChatTransport } from 'ai';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Bot, 
  User, 
  Send, 
  Square, 
  RotateCcw, 
  Trash2, 
  Copy, 
  Download,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { CustomUIMessage, AIStatus } from '@/types/ai-sdk-ui';
import { MessageRenderer } from './message-renderer';
import { ToolCallRenderer } from './tool-call-renderer';
import { ErrorDisplay } from './error-display';
import { LoadingIndicator } from './loading-indicator';

interface AdvancedChatProps {
  id?: string;
  initialMessages?: CustomUIMessage[];
  api?: string;
  title?: string;
  subtitle?: string;
  placeholder?: string;
  maxHeight?: string;
  showHeader?: boolean;
  showTimestamps?: boolean;
  showTokenCount?: boolean;
  allowFileUpload?: boolean;
  allowRegenerate?: boolean;
  allowDelete?: boolean;
  allowCopy?: boolean;
  allowExport?: boolean;
  className?: string;
  onMessageSent?: (message: CustomUIMessage) => void;
  onMessageReceived?: (message: CustomUIMessage) => void;
  onError?: (error: Error) => void;
}

export function AdvancedChat({
  id,
  initialMessages = [],
  api = '/api/chat',
  title = 'AI Assistant',
  subtitle = 'Powered by AI SDK V5',
  placeholder = 'Type your message...',
  maxHeight = '600px',
  showHeader = true,
  showTimestamps = false,
  showTokenCount = false,
  allowFileUpload = true,
  allowRegenerate = true,
  allowDelete = true,
  allowCopy = true,
  allowExport = true,
  className = '',
  onMessageSent,
  onMessageReceived,
  onError,
}: AdvancedChatProps) {
  const [input, setInput] = useState('');
  const [files, setFiles] = useState<FileList | undefined>();
  const [notification, setNotification] = useState<{ message: string; type: 'info' | 'error' | 'success' } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const {
    messages,
    sendMessage,
    reload,
    stop,
    regenerate,
    setMessages,
    isLoading,
    status,
    error,
  } = useChat({
    id,
    messages: initialMessages,
    transport: new DefaultChatTransport({ api }),
    onFinish: (message, options) => {
      onMessageReceived?.(message as CustomUIMessage);
      if (showTokenCount && options.usage) {
        setNotification({
          message: `Used ${options.usage.totalTokens} tokens`,
          type: 'info'
        });
        setTimeout(() => setNotification(null), 3000);
      }
    },
    onError: (err) => {
      onError?.(err);
      setNotification({
        message: 'An error occurred while processing your message',
        type: 'error'
      });
      setTimeout(() => setNotification(null), 5000);
    },
    onData: (data) => {
      // Handle transient data parts
      if (data.type === 'data-notification') {
        setNotification({
          message: data.data.message,
          type: data.data.level === 'error' ? 'error' : 'info'
        });
        setTimeout(() => setNotification(null), 3000);
      }
    },
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const messageData = {
      text: input,
      ...(files && { files })
    };

    sendMessage(messageData);
    onMessageSent?.(messageData as any);
    
    setInput('');
    setFiles(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFiles(e.target.files);
    }
  };

  const handleDeleteMessage = (messageId: string) => {
    setMessages(messages.filter(m => m.id !== messageId));
  };

  const handleCopyMessage = (message: CustomUIMessage) => {
    const textContent = message.parts
      .filter(part => part.type === 'text')
      .map(part => (part as any).text)
      .join('');
    
    navigator.clipboard.writeText(textContent);
    setNotification({
      message: 'Message copied to clipboard',
      type: 'success'
    });
    setTimeout(() => setNotification(null), 2000);
  };

  const handleExportChat = () => {
    const chatData = {
      id,
      title,
      messages,
      exportedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(chatData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-${id || 'export'}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (status: AIStatus) => {
    switch (status) {
      case 'streaming':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      case 'ready':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <Card className={`flex flex-col ${className}`} style={{ maxHeight }}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 bg-primary rounded-lg">
                <Bot className="h-5 w-5 text-primary-foreground" />
              </div>
              <div>
                <CardTitle className="text-lg">{title}</CardTitle>
                <p className="text-sm text-muted-foreground">{subtitle}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                {getStatusIcon(status)}
                <span className="capitalize">{status}</span>
              </Badge>
              {allowExport && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleExportChat}
                  disabled={messages.length === 0}
                >
                  <Download className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
          {notification && (
            <div className={`mt-2 p-2 rounded-md text-sm ${
              notification.type === 'error' 
                ? 'bg-destructive/10 text-destructive' 
                : notification.type === 'success'
                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                : 'bg-muted text-muted-foreground'
            }`}>
              {notification.message}
            </div>
          )}
        </CardHeader>
      )}

      <CardContent className="flex-1 flex flex-col gap-4 p-4">
        <ScrollArea className="flex-1 pr-4" ref={scrollAreaRef}>
          <div className="space-y-4">
            {messages.length === 0 && (
              <div className="text-center text-muted-foreground py-8">
                <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Start a conversation with the AI assistant</p>
                <p className="text-sm mt-1">Ask questions, request help, or explore AI capabilities</p>
              </div>
            )}

            {messages.map((message, index) => (
              <div key={message.id} className="group">
                <MessageRenderer
                  message={message}
                  isLast={index === messages.length - 1}
                  showTimestamp={showTimestamps}
                  onCopy={allowCopy ? () => handleCopyMessage(message) : undefined}
                  onDelete={allowDelete ? () => handleDeleteMessage(message.id) : undefined}
                  onRegenerate={allowRegenerate && message.role === 'assistant' && index === messages.length - 1 ? regenerate : undefined}
                />
                {index < messages.length - 1 && <Separator className="my-4" />}
              </div>
            ))}

            {error && (
              <ErrorDisplay
                error={error}
                onRetry={reload}
                className="mt-4"
              />
            )}

            {(status === 'submitted' || status === 'streaming') && (
              <LoadingIndicator
                status={status}
                message={status === 'submitted' ? 'Sending message...' : 'AI is thinking...'}
              />
            )}
          </div>
        </ScrollArea>

        <div className="space-y-2">
          {files && files.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {Array.from(files).map((file, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {file.name}
                </Badge>
              ))}
            </div>
          )}

          <form onSubmit={handleSubmit} className="flex gap-2">
            <div className="flex-1 relative">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder={placeholder}
                disabled={status !== 'ready'}
                className="pr-12"
                maxLength={4000}
              />
              {allowFileUpload && (
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileSelect}
                  className="hidden"
                  accept="image/*,text/*,.pdf,.doc,.docx"
                />
              )}
            </div>
            
            {allowFileUpload && (
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => fileInputRef.current?.click()}
                disabled={status !== 'ready'}
              >
                📎
              </Button>
            )}

            {status === 'streaming' || status === 'submitted' ? (
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={stop}
              >
                <Square className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                type="submit"
                size="icon"
                disabled={!input.trim() || status !== 'ready'}
              >
                <Send className="h-4 w-4" />
              </Button>
            )}
          </form>

          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <span>{input.length}/4000</span>
            {messages.length > 0 && (
              <span>{messages.length} message{messages.length !== 1 ? 's' : ''}</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
