'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Copy, 
  Trash2, 
  RotateCcw, 
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  Image as ImageIcon,
  Code,
  Wrench
} from 'lucide-react';
import { CustomUIMessage, MessagePart } from '@/types/ai-sdk-ui';
import { ToolCallRenderer } from './tool-call-renderer';
import { DataPartRenderer } from './data-part-renderer';

interface MessageRendererProps {
  message: CustomUIMessage;
  isLast?: boolean;
  showTimestamp?: boolean;
  onCopy?: () => void;
  onDelete?: () => void;
  onRegenerate?: () => void;
  className?: string;
}

export function MessageRenderer({
  message,
  isLast = false,
  showTimestamp = false,
  onCopy,
  onDelete,
  onRegenerate,
  className = '',
}: MessageRendererProps) {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';
  const isSystem = message.role === 'system';

  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getMessageIcon = () => {
    if (isUser) {
      return (
        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
          <User className="h-4 w-4 text-primary-foreground" />
        </div>
      );
    }
    
    if (isSystem) {
      return (
        <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center flex-shrink-0">
          <AlertCircle className="h-4 w-4 text-muted-foreground" />
        </div>
      );
    }

    return (
      <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center flex-shrink-0">
        <Bot className="h-4 w-4 text-secondary-foreground" />
      </div>
    );
  };

  const renderTextPart = (part: any, index: number) => {
    if (part.type !== 'text') return null;
    
    return (
      <div key={`text-${index}`} className="prose prose-sm max-w-none dark:prose-invert">
        <div className="whitespace-pre-wrap break-words">
          {part.text}
        </div>
      </div>
    );
  };

  const renderFilePart = (part: any, index: number) => {
    if (part.type !== 'file') return null;

    const getFileIcon = (mimeType: string) => {
      if (mimeType.startsWith('image/')) return <ImageIcon className="h-4 w-4" />;
      if (mimeType.includes('text') || mimeType.includes('json')) return <FileText className="h-4 w-4" />;
      if (mimeType.includes('code')) return <Code className="h-4 w-4" />;
      return <FileText className="h-4 w-4" />;
    };

    return (
      <Card key={`file-${index}`} className="mt-2">
        <CardContent className="p-3">
          <div className="flex items-center gap-2">
            {getFileIcon(part.mimeType)}
            <span className="text-sm font-medium">{part.name}</span>
            <Badge variant="outline" className="text-xs">
              {(part.size / 1024).toFixed(1)} KB
            </Badge>
          </div>
          {part.mimeType.startsWith('image/') && part.url && (
            <img 
              src={part.url} 
              alt={part.name}
              className="mt-2 max-w-full h-auto rounded-md"
              style={{ maxHeight: '200px' }}
            />
          )}
        </CardContent>
      </Card>
    );
  };

  const renderToolPart = (part: any, index: number) => {
    if (!part.type.startsWith('tool-')) return null;
    
    return (
      <ToolCallRenderer
        key={`tool-${index}`}
        toolCall={part}
        className="mt-2"
      />
    );
  };

  const renderDataPart = (part: any, index: number) => {
    if (!part.type.startsWith('data-')) return null;
    
    return (
      <DataPartRenderer
        key={`data-${index}`}
        dataPart={part}
        className="mt-2"
      />
    );
  };

  const renderPart = (part: MessagePart, index: number) => {
    switch (part.type) {
      case 'text':
        return renderTextPart(part, index);
      case 'file':
        return renderFilePart(part, index);
      default:
        if (part.type.startsWith('tool-')) {
          return renderToolPart(part, index);
        }
        if (part.type.startsWith('data-')) {
          return renderDataPart(part, index);
        }
        // Fallback for unknown part types
        return (
          <div key={`unknown-${index}`} className="mt-2 p-2 bg-muted rounded-md">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Wrench className="h-4 w-4" />
              <span>Unknown part type: {part.type}</span>
            </div>
            <pre className="mt-1 text-xs overflow-x-auto">
              {JSON.stringify(part, null, 2)}
            </pre>
          </div>
        );
    }
  };

  const hasActions = onCopy || onDelete || onRegenerate;

  return (
    <div className={`flex gap-3 group ${isUser ? 'flex-row-reverse' : 'flex-row'} ${className}`}>
      <div className={`flex gap-3 max-w-[85%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {getMessageIcon()}
        
        <div className="flex-1 space-y-1">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {isUser ? 'You' : isSystem ? 'System' : 'Assistant'}
            </span>
            {showTimestamp && message.metadata?.createdAt && (
              <span className="text-xs text-muted-foreground flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {formatTimestamp(message.metadata.createdAt)}
              </span>
            )}
            {message.metadata?.model && (
              <Badge variant="outline" className="text-xs">
                {message.metadata.model}
              </Badge>
            )}
          </div>
          
          <div className={`rounded-lg px-4 py-3 ${
            isUser 
              ? 'bg-primary text-primary-foreground' 
              : isSystem
              ? 'bg-muted text-muted-foreground'
              : 'bg-secondary text-secondary-foreground'
          }`}>
            <div className="space-y-2">
              {message.parts.map((part, index) => renderPart(part, index))}
            </div>
            
            {message.metadata?.totalTokens && (
              <div className="mt-2 pt-2 border-t border-current/20">
                <span className="text-xs opacity-70">
                  {message.metadata.totalTokens} tokens
                </span>
              </div>
            )}
          </div>

          {hasActions && (
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {onCopy && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onCopy}
                  className="h-6 px-2"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              )}
              {onRegenerate && isLast && isAssistant && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRegenerate}
                  className="h-6 px-2"
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDelete}
                  className="h-6 px-2 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
