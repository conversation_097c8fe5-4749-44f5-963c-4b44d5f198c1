'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  User, 
  Settings, 
  LogOut, 
  CreditCard, 
  Shield,
  HelpCircle,
  Cloud,
  Monitor,
  Bot,
  ChevronDown,
  Loader2
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { storage, AVATARS_BUCKET_ID } from '@/lib/appwrite';

interface UserNavProps {
  className?: string;
}

export function UserNav({ className = '' }: UserNavProps) {
  const router = useRouter();
  const { user, profile, logout, isLoading } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const getAvatarUrl = (avatarId: string) => {
    try {
      return storage.getFilePreview(AVATARS_BUCKET_ID, avatarId, 100, 100);
    } catch (error) {
      return null;
    }
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'enterprise':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      case 'professional':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'starter':
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Loading...</span>
      </div>
    );
  }

  if (!user || !profile) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Link href="/auth/login">
          <Button variant="ghost" size="sm">
            Sign In
          </Button>
        </Link>
        <Link href="/auth/register">
          <Button size="sm">
            Sign Up
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className={`relative h-10 w-auto px-2 ${className}`}>
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage 
                src={profile.avatar ? getAvatarUrl(profile.avatar)?.toString() : undefined} 
                alt={profile.name}
              />
              <AvatarFallback className="text-xs">
                {getUserInitials(profile.name)}
              </AvatarFallback>
            </Avatar>
            
            <div className="hidden md:flex flex-col items-start">
              <span className="text-sm font-medium">{profile.name}</span>
              <Badge 
                variant="outline" 
                className={`text-xs ${getPlanColor(profile.subscription.plan)}`}
              >
                {profile.subscription.plan}
              </Badge>
            </div>
            
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-64" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage 
                  src={profile.avatar ? getAvatarUrl(profile.avatar)?.toString() : undefined} 
                  alt={profile.name}
                />
                <AvatarFallback className="text-xs">
                  {getUserInitials(profile.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <p className="text-sm font-medium leading-none">{profile.name}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user.email}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge 
                variant="outline" 
                className={`text-xs ${getPlanColor(profile.subscription.plan)}`}
              >
                {profile.subscription.plan}
              </Badge>
              <Badge 
                variant={profile.subscription.status === 'active' ? 'default' : 'secondary'}
                className="text-xs"
              >
                {profile.subscription.status}
              </Badge>
            </div>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="cursor-pointer">
              <Cloud className="mr-2 h-4 w-4" />
              <span>Dashboard</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/vm-manager" className="cursor-pointer">
              <Monitor className="mr-2 h-4 w-4" />
              <span>VM Manager</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/workspace-ai" className="cursor-pointer">
              <Bot className="mr-2 h-4 w-4" />
              <span>AI Workspace</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/profile" className="cursor-pointer">
              <User className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/settings" className="cursor-pointer">
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/billing" className="cursor-pointer">
              <CreditCard className="mr-2 h-4 w-4" />
              <span>Billing</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/security" className="cursor-pointer">
              <Shield className="mr-2 h-4 w-4" />
              <span>Security</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/help" className="cursor-pointer">
              <HelpCircle className="mr-2 h-4 w-4" />
              <span>Help & Support</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          className="cursor-pointer text-red-600 dark:text-red-400"
          onClick={handleLogout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Simplified version for mobile or compact layouts
export function UserNavCompact({ className = '' }: UserNavProps) {
  const { user, profile, logout, isLoading } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const getAvatarUrl = (avatarId: string) => {
    try {
      return storage.getFilePreview(AVATARS_BUCKET_ID, avatarId, 100, 100);
    } catch (error) {
      return null;
    }
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
      </div>
    );
  }

  if (!user || !profile) {
    return (
      <div className={`flex flex-col gap-2 ${className}`}>
        <Link href="/auth/login" className="w-full">
          <Button variant="ghost" size="sm" className="w-full">
            Sign In
          </Button>
        </Link>
        <Link href="/auth/register" className="w-full">
          <Button size="sm" className="w-full">
            Sign Up
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* User Info */}
      <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
        <Avatar className="h-10 w-10">
          <AvatarImage 
            src={profile.avatar ? getAvatarUrl(profile.avatar)?.toString() : undefined} 
            alt={profile.name}
          />
          <AvatarFallback className="text-sm">
            {getUserInitials(profile.name)}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{profile.name}</p>
          <p className="text-xs text-muted-foreground truncate">{user.email}</p>
          <Badge variant="outline" className="text-xs mt-1">
            {profile.subscription.plan}
          </Badge>
        </div>
      </div>

      {/* Navigation Links */}
      <div className="space-y-1">
        <Link href="/dashboard" className="block">
          <Button variant="ghost" size="sm" className="w-full justify-start">
            <Cloud className="mr-2 h-4 w-4" />
            Dashboard
          </Button>
        </Link>
        
        <Link href="/profile" className="block">
          <Button variant="ghost" size="sm" className="w-full justify-start">
            <User className="mr-2 h-4 w-4" />
            Profile
          </Button>
        </Link>
        
        <Link href="/settings" className="block">
          <Button variant="ghost" size="sm" className="w-full justify-start">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </Link>
      </div>

      {/* Logout */}
      <Button 
        variant="ghost" 
        size="sm" 
        className="w-full justify-start text-red-600 dark:text-red-400"
        onClick={handleLogout}
      >
        <LogOut className="mr-2 h-4 w-4" />
        Log out
      </Button>
    </div>
  );
}
