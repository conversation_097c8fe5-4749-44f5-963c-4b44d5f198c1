'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight, 
  Rocket, 
  Clock, 
  Shield, 
  Zap,
  CheckCircle,
  Star
} from 'lucide-react';

const benefits = [
  {
    icon: Clock,
    title: 'Start in seconds',
    description: 'No complex setup or configuration required'
  },
  {
    icon: Shield,
    title: 'Enterprise security',
    description: 'VM-level isolation and encrypted connections'
  },
  {
    icon: Zap,
    title: 'Lightning fast',
    description: 'Sub-30 second boot times for instant productivity'
  }
];

const guarantees = [
  'Free 14-day trial',
  'No credit card required',
  'Cancel anytime',
  '99.9% uptime SLA'
];

export function CTASection() {
  return (
    <section className="py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-white/5 rounded-full blur-xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-20 w-24 h-24 bg-white/10 rounded-full blur-xl animate-pulse delay-2000" />
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-white/5 rounded-full blur-xl animate-pulse delay-3000" />
      </div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Header */}
          <div className="mb-12">
            <Badge variant="outline" className="mb-6 bg-white/10 border-white/20 text-white">
              <Rocket className="h-4 w-4 mr-2" />
              Ready to get started?
            </Badge>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Transform your development
              <span className="block">workflow today</span>
            </h2>
            
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              Join thousands of developers who have already revolutionized their 
              development process with Omnispace's AI-powered remote workspaces.
            </p>
          </div>

          {/* Benefits Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-full mb-4">
                  <benefit.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold mb-2">{benefit.title}</h3>
                <p className="text-blue-100 text-sm">{benefit.description}</p>
              </div>
            ))}
          </div>

          {/* Main CTA */}
          <div className="space-y-6 mb-12">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              
              <Link href="#demo">
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg"
                >
                  Watch Demo
                </Button>
              </Link>
            </div>

            {/* Guarantees */}
            <div className="flex flex-wrap justify-center gap-6 text-sm text-blue-100">
              {guarantees.map((guarantee, index) => (
                <div key={index} className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  <span>{guarantee}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Social Proof */}
          <div className="border-t border-white/20 pt-12">
            <div className="flex flex-col md:flex-row items-center justify-center gap-8 text-blue-100">
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <span className="font-semibold">4.9/5</span>
                <span>from 500+ reviews</span>
              </div>
              
              <div className="hidden md:block w-px h-6 bg-white/20" />
              
              <div className="text-center md:text-left">
                <span className="font-semibold">50,000+</span> developers trust Omnispace
              </div>
              
              <div className="hidden md:block w-px h-6 bg-white/20" />
              
              <div className="text-center md:text-left">
                <span className="font-semibold">99.9%</span> uptime SLA
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
