'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Cloud, 
  Monitor, 
  Bot, 
  Shield, 
  Zap, 
  Cpu, 
  Globe, 
  Lock,
  Layers,
  Terminal,
  Code,
  Workflow
} from 'lucide-react';

const features = [
  {
    category: 'Virtualization',
    icon: Cloud,
    color: 'blue',
    items: [
      {
        title: 'Firecracker MicroVMs',
        description: 'Ultra-lightweight virtualization with minimal overhead and maximum security isolation.',
        icon: Layers,
        benefits: ['Sub-second boot times', 'Minimal resource usage', 'Strong isolation'],
      },
      {
        title: 'Docker Integration',
        description: 'Seamless Ubuntu containers running inside MicroVMs for familiar development environments.',
        icon: Code,
        benefits: ['Standard Ubuntu environment', 'Container portability', 'Easy deployment'],
      },
    ],
  },
  {
    category: 'Remote Access',
    icon: Monitor,
    color: 'green',
    items: [
      {
        title: 'Browser-based VNC',
        description: 'Access your desktop environments directly from any web browser without additional software.',
        icon: Globe,
        benefits: ['No client installation', 'Cross-platform access', 'Secure connections'],
      },
      {
        title: 'Real-time Interaction',
        description: 'Low-latency remote desktop experience with full keyboard and mouse support.',
        icon: Zap,
        benefits: ['Minimal latency', 'Full desktop experience', 'Responsive interface'],
      },
    ],
  },
  {
    category: 'AI Integration',
    icon: Bot,
    color: 'purple',
    items: [
      {
        title: 'AI-Powered Computer Use',
        description: 'Advanced AI agents that can interact with and control your remote desktop environments.',
        icon: Bot,
        benefits: ['Automated workflows', 'Intelligent assistance', 'Natural language control'],
      },
      {
        title: 'Workspace Automation',
        description: 'Automate development tasks, file operations, and environment setup with AI assistance.',
        icon: Workflow,
        benefits: ['Task automation', 'Smart suggestions', 'Productivity boost'],
      },
    ],
  },
  {
    category: 'Security & Performance',
    icon: Shield,
    color: 'red',
    items: [
      {
        title: 'Enterprise Security',
        description: 'Multi-layered security with VM isolation, encrypted connections, and access controls.',
        icon: Lock,
        benefits: ['VM-level isolation', 'Encrypted traffic', 'Access management'],
      },
      {
        title: 'Scalable Architecture',
        description: 'Designed for both single-user and multi-tenant deployments with efficient resource usage.',
        icon: Cpu,
        benefits: ['Auto-scaling', 'Resource optimization', 'Multi-tenancy'],
      },
    ],
  },
];

const getColorClasses = (color: string) => {
  const colors = {
    blue: {
      bg: 'bg-blue-50 dark:bg-blue-900/20',
      border: 'border-blue-200 dark:border-blue-800',
      text: 'text-blue-600 dark:text-blue-400',
      icon: 'text-blue-500',
    },
    green: {
      bg: 'bg-green-50 dark:bg-green-900/20',
      border: 'border-green-200 dark:border-green-800',
      text: 'text-green-600 dark:text-green-400',
      icon: 'text-green-500',
    },
    purple: {
      bg: 'bg-purple-50 dark:bg-purple-900/20',
      border: 'border-purple-200 dark:border-purple-800',
      text: 'text-purple-600 dark:text-purple-400',
      icon: 'text-purple-500',
    },
    red: {
      bg: 'bg-red-50 dark:bg-red-900/20',
      border: 'border-red-200 dark:border-red-800',
      text: 'text-red-600 dark:text-red-400',
      icon: 'text-red-500',
    },
  };
  return colors[color as keyof typeof colors] || colors.blue;
};

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-gray-50 dark:bg-gray-900/50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">
              <Zap className="h-4 w-4 mr-2" />
              Powerful Features
            </Badge>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Everything you need for
              <span className="block text-blue-600 dark:text-blue-400">
                modern development
              </span>
            </h2>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Omnispace combines cutting-edge virtualization, seamless remote access, 
              and intelligent AI assistance to create the ultimate development platform.
            </p>
          </div>

          {/* Features Grid */}
          <div className="space-y-16">
            {features.map((category, categoryIndex) => {
              const colorClasses = getColorClasses(category.color);
              
              return (
                <div key={category.category} className="space-y-8">
                  {/* Category Header */}
                  <div className="flex items-center gap-4">
                    <div className={`p-3 rounded-lg ${colorClasses.bg} ${colorClasses.border} border`}>
                      <category.icon className={`h-6 w-6 ${colorClasses.icon}`} />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                        {category.category}
                      </h3>
                      <div className={`h-1 w-16 ${colorClasses.bg} rounded-full mt-2`} />
                    </div>
                  </div>

                  {/* Feature Cards */}
                  <div className="grid md:grid-cols-2 gap-8">
                    {category.items.map((feature, featureIndex) => (
                      <Card 
                        key={feature.title}
                        className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md"
                      >
                        <CardHeader>
                          <div className="flex items-start gap-4">
                            <div className={`p-2 rounded-lg ${colorClasses.bg} ${colorClasses.border} border group-hover:scale-110 transition-transform duration-300`}>
                              <feature.icon className={`h-5 w-5 ${colorClasses.icon}`} />
                            </div>
                            <div className="flex-1">
                              <CardTitle className="text-xl text-gray-900 dark:text-white mb-2">
                                {feature.title}
                              </CardTitle>
                              <p className="text-gray-600 dark:text-gray-300">
                                {feature.description}
                              </p>
                            </div>
                          </div>
                        </CardHeader>
                        
                        <CardContent>
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                              Key Benefits:
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {feature.benefits.map((benefit, benefitIndex) => (
                                <Badge 
                                  key={benefitIndex}
                                  variant="secondary" 
                                  className={`text-xs ${colorClasses.bg} ${colorClasses.text} border-0`}
                                >
                                  {benefit}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
              Ready to experience the future of remote development?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="#demo" 
                className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
              >
                <Terminal className="mr-2 h-5 w-5" />
                Try Interactive Demo
              </a>
              <a 
                href="#architecture" 
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                Learn More
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
