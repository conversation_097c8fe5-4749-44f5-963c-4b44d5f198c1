'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Building, 
  GraduationCap, 
  Code, 
  Bot, 
  Globe,
  ArrowRight,
  CheckCircle,
  Zap,
  Shield,
  Cpu,
  Clock
} from 'lucide-react';

const useCases = [
  {
    id: 'development',
    title: 'Development Teams',
    subtitle: 'Scalable development environments for modern teams',
    icon: Code,
    color: 'blue',
    description: 'Empower your development teams with instant, consistent, and scalable cloud-based development environments.',
    benefits: [
      'Instant environment provisioning',
      'Consistent development setups',
      'Team collaboration tools',
      'Version control integration',
      'Automated CI/CD pipelines',
      'Resource scaling on demand'
    ],
    scenarios: [
      {
        title: 'Remote Development',
        description: 'Enable distributed teams to work on the same codebase with identical environments.',
        metrics: '90% faster onboarding'
      },
      {
        title: 'Testing & QA',
        description: 'Spin up isolated environments for testing different configurations and versions.',
        metrics: '75% reduction in setup time'
      },
      {
        title: 'Code Reviews',
        description: 'Create temporary environments for reviewing and testing pull requests.',
        metrics: '50% faster review cycles'
      }
    ],
    testimonial: {
      quote: "Omnispace transformed our development workflow. Our team can now focus on coding instead of environment setup.",
      author: "Sarah Chen",
      role: "Lead Developer at TechCorp"
    }
  },
  {
    id: 'enterprise',
    title: 'Enterprise Solutions',
    subtitle: 'Enterprise-grade security and compliance',
    icon: Building,
    color: 'green',
    description: 'Meet enterprise requirements with advanced security, compliance, and management features.',
    benefits: [
      'Enterprise-grade security',
      'Compliance certifications',
      'Advanced user management',
      'Audit trails and logging',
      'Custom integrations',
      'SLA guarantees'
    ],
    scenarios: [
      {
        title: 'Secure Development',
        description: 'Isolated development environments that meet strict security requirements.',
        metrics: '99.9% uptime SLA'
      },
      {
        title: 'Contractor Access',
        description: 'Provide secure, time-limited access to external developers and contractors.',
        metrics: 'Zero security incidents'
      },
      {
        title: 'Compliance Workflows',
        description: 'Maintain compliance with industry standards and regulations.',
        metrics: 'SOC 2 Type II certified'
      }
    ],
    testimonial: {
      quote: "The security and compliance features give us confidence to use Omnispace for our most sensitive projects.",
      author: "Michael Rodriguez",
      role: "CTO at FinanceSecure"
    }
  },
  {
    id: 'education',
    title: 'Education & Training',
    subtitle: 'Learning environments for students and professionals',
    icon: GraduationCap,
    color: 'purple',
    description: 'Create consistent learning environments for coding bootcamps, universities, and corporate training.',
    benefits: [
      'Pre-configured learning environments',
      'Student progress tracking',
      'Instructor oversight tools',
      'Assignment management',
      'Collaborative learning spaces',
      'Cost-effective scaling'
    ],
    scenarios: [
      {
        title: 'Coding Bootcamps',
        description: 'Provide students with identical development environments from day one.',
        metrics: '100+ students supported'
      },
      {
        title: 'Corporate Training',
        description: 'Train employees on new technologies with hands-on practice environments.',
        metrics: '85% completion rate'
      },
      {
        title: 'Research Projects',
        description: 'Enable collaborative research with shared computational resources.',
        metrics: '60% cost reduction'
      }
    ],
    testimonial: {
      quote: "Our students can focus on learning to code instead of struggling with environment setup issues.",
      author: "Dr. Emily Watson",
      role: "Computer Science Professor"
    }
  }
];

const getColorClasses = (color: string) => {
  const colors = {
    blue: {
      bg: 'bg-blue-50 dark:bg-blue-900/20',
      border: 'border-blue-200 dark:border-blue-800',
      text: 'text-blue-600 dark:text-blue-400',
      button: 'bg-blue-600 hover:bg-blue-700'
    },
    green: {
      bg: 'bg-green-50 dark:bg-green-900/20',
      border: 'border-green-200 dark:border-green-800',
      text: 'text-green-600 dark:text-green-400',
      button: 'bg-green-600 hover:bg-green-700'
    },
    purple: {
      bg: 'bg-purple-50 dark:bg-purple-900/20',
      border: 'border-purple-200 dark:border-purple-800',
      text: 'text-purple-600 dark:text-purple-400',
      button: 'bg-purple-600 hover:bg-purple-700'
    }
  };
  return colors[color as keyof typeof colors] || colors.blue;
};

export function UseCasesSection() {
  const [selectedUseCase, setSelectedUseCase] = useState('development');
  const currentUseCase = useCases.find(uc => uc.id === selectedUseCase) || useCases[0];
  const colorClasses = getColorClasses(currentUseCase.color);

  return (
    <section id="use-cases" className="py-24 bg-gray-50 dark:bg-gray-900/50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">
              <Users className="h-4 w-4 mr-2" />
              Use Cases
            </Badge>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Built for every
              <span className="block text-blue-600 dark:text-blue-400">
                development scenario
              </span>
            </h2>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              From individual developers to enterprise teams, Omnispace adapts to your 
              specific needs and scales with your organization.
            </p>
          </div>

          {/* Use Case Selector */}
          <div className="flex flex-col lg:flex-row gap-4 mb-12">
            {useCases.map((useCase) => (
              <Button
                key={useCase.id}
                variant={selectedUseCase === useCase.id ? "default" : "outline"}
                className={`flex-1 h-auto p-6 ${
                  selectedUseCase === useCase.id 
                    ? getColorClasses(useCase.color).button + ' text-white'
                    : ''
                }`}
                onClick={() => setSelectedUseCase(useCase.id)}
              >
                <div className="flex items-center gap-3">
                  <useCase.icon className="h-6 w-6" />
                  <div className="text-left">
                    <div className="font-semibold">{useCase.title}</div>
                    <div className="text-sm opacity-80">{useCase.subtitle}</div>
                  </div>
                </div>
              </Button>
            ))}
          </div>

          {/* Selected Use Case Details */}
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Left Column - Overview */}
            <div className="space-y-8">
              <div>
                <div className={`inline-flex items-center gap-3 p-4 rounded-lg ${colorClasses.bg} ${colorClasses.border} border mb-6`}>
                  <currentUseCase.icon className={`h-8 w-8 ${colorClasses.text}`} />
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {currentUseCase.title}
                    </h3>
                    <p className={`text-sm ${colorClasses.text}`}>
                      {currentUseCase.subtitle}
                    </p>
                  </div>
                </div>
                
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                  {currentUseCase.description}
                </p>
              </div>

              {/* Benefits */}
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  Key Benefits
                </h4>
                <div className="grid grid-cols-1 gap-3">
                  {currentUseCase.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <CheckCircle className={`h-5 w-5 ${colorClasses.text} flex-shrink-0`} />
                      <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Testimonial */}
              <Card className={`${colorClasses.bg} ${colorClasses.border} border`}>
                <CardContent className="p-6">
                  <blockquote className="text-gray-700 dark:text-gray-300 mb-4">
                    "{currentUseCase.testimonial.quote}"
                  </blockquote>
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 rounded-full ${colorClasses.text} bg-current opacity-20`} />
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">
                        {currentUseCase.testimonial.author}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {currentUseCase.testimonial.role}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Scenarios */}
            <div className="space-y-6">
              <h4 className="text-xl font-bold text-gray-900 dark:text-white">
                Real-World Scenarios
              </h4>
              
              {currentUseCase.scenarios.map((scenario, index) => (
                <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg text-gray-900 dark:text-white mb-2">
                          {scenario.title}
                        </CardTitle>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                          {scenario.description}
                        </p>
                      </div>
                      <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full ${colorClasses.bg} ${colorClasses.text} text-sm font-medium`}>
                      <Zap className="h-4 w-4" />
                      {scenario.metrics}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Bottom Stats */}
          <div className="mt-16 grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                10k+
              </div>
              <div className="text-gray-600 dark:text-gray-300">
                Active Workspaces
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                99.9%
              </div>
              <div className="text-gray-600 dark:text-gray-300">
                Uptime SLA
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                &lt;30s
              </div>
              <div className="text-gray-600 dark:text-gray-300">
                Average Boot Time
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">
                500+
              </div>
              <div className="text-gray-600 dark:text-gray-300">
                Enterprise Customers
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
