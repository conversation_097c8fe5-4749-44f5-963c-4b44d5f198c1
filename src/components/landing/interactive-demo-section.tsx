'use client';

import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Monitor, 
  Bot, 
  Terminal, 
  Code, 
  Zap,
  ArrowRight,
  CheckCircle,
  Clock,
  Cpu,
  HardDrive,
  Network
} from 'lucide-react';

const demoScenarios = [
  {
    id: 'vm-creation',
    title: 'VM Creation',
    description: 'Watch how quickly you can spin up a new development environment',
    icon: Monitor,
    duration: '30 seconds',
    steps: [
      'Select VM configuration',
      'Choose Ubuntu image',
      'Firecracker boots MicroVM',
      'Docker container starts',
      'VNC connection established',
      'Ready to code!'
    ]
  },
  {
    id: 'ai-assistance',
    title: 'AI Integration',
    description: 'See AI agents interact with and control your workspace',
    icon: Bot,
    duration: '45 seconds',
    steps: [
      'AI agent connects to workspace',
      'Natural language command given',
      'AI navigates desktop interface',
      'Code is generated and executed',
      'Results are analyzed',
      'Task completed automatically'
    ]
  },
  {
    id: 'development',
    title: 'Development Workflow',
    description: 'Experience a complete development workflow in the cloud',
    icon: Code,
    duration: '60 seconds',
    steps: [
      'Open VS Code in browser',
      'Clone repository',
      'Install dependencies',
      'Run development server',
      'Make code changes',
      'Deploy to production'
    ]
  }
];

const liveMetrics = [
  { label: 'Active VMs', value: '1,247', icon: Monitor, trend: '+12%' },
  { label: 'Boot Time', value: '23s', icon: Clock, trend: '-5s' },
  { label: 'CPU Usage', value: '67%', icon: Cpu, trend: 'Normal' },
  { label: 'Network I/O', value: '2.4 GB/s', icon: Network, trend: '+8%' }
];

export function InteractiveDemoSection() {
  const [selectedDemo, setSelectedDemo] = useState('vm-creation');
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [metrics, setMetrics] = useState(liveMetrics);

  const currentScenario = demoScenarios.find(s => s.id === selectedDemo) || demoScenarios[0];

  // Simulate demo progression
  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setCurrentStep(prev => {
          if (prev >= currentScenario.steps.length - 1) {
            setIsPlaying(false);
            return 0;
          }
          return prev + 1;
        });
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [isPlaying, currentScenario.steps.length]);

  // Simulate live metrics updates
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => prev.map(metric => ({
        ...metric,
        value: metric.label === 'Active VMs' 
          ? `${Math.floor(Math.random() * 100) + 1200}`
          : metric.label === 'Boot Time'
          ? `${Math.floor(Math.random() * 10) + 20}s`
          : metric.label === 'CPU Usage'
          ? `${Math.floor(Math.random() * 30) + 50}%`
          : `${(Math.random() * 2 + 2).toFixed(1)} GB/s`
      })));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const startDemo = () => {
    setCurrentStep(0);
    setIsPlaying(true);
  };

  return (
    <section id="demo" className="py-24 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">
              <Play className="h-4 w-4 mr-2" />
              Interactive Demo
            </Badge>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              See Omnispace
              <span className="block text-blue-600 dark:text-blue-400">
                in action
              </span>
            </h2>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Experience the power of cloud-based development environments with our 
              interactive demos. No signup required.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Demo Controls */}
            <div className="space-y-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                Choose a Demo
              </h3>
              
              {demoScenarios.map((scenario) => (
                <Card 
                  key={scenario.id}
                  className={`cursor-pointer transition-all duration-300 ${
                    selectedDemo === scenario.id 
                      ? 'ring-2 ring-blue-500 shadow-lg' 
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedDemo(scenario.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <scenario.icon className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                          {scenario.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                          {scenario.description}
                        </p>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          {scenario.duration}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Live Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Live Platform Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {metrics.map((metric, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <metric.icon className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-300">
                          {metric.label}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900 dark:text-white">
                          {metric.value}
                        </div>
                        <div className="text-xs text-green-600">
                          {metric.trend}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Demo Viewer */}
            <div className="lg:col-span-2 space-y-6">
              {/* Demo Screen */}
              <Card className="overflow-hidden">
                <CardHeader className="bg-gray-50 dark:bg-gray-800 border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <currentScenario.icon className="h-5 w-5 text-blue-600" />
                      <CardTitle className="text-lg">{currentScenario.title}</CardTitle>
                    </div>
                    <Button 
                      onClick={startDemo}
                      disabled={isPlaying}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      {isPlaying ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                          Running...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Start Demo
                        </>
                      )}
                    </Button>
                  </div>
                </CardHeader>
                
                <CardContent className="p-0">
                  {/* Simulated Terminal/Desktop */}
                  <div className="bg-gray-900 text-green-400 p-6 font-mono text-sm min-h-[300px]">
                    <div className="mb-4">
                      <span className="text-blue-400">omnispace@demo:~$</span> {currentScenario.title.toLowerCase().replace(' ', '-')}
                    </div>
                    
                    {currentScenario.steps.map((step, index) => (
                      <div 
                        key={index}
                        className={`mb-2 transition-all duration-500 ${
                          index <= currentStep 
                            ? 'opacity-100' 
                            : 'opacity-30'
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          {index < currentStep ? (
                            <CheckCircle className="h-4 w-4 text-green-400" />
                          ) : index === currentStep && isPlaying ? (
                            <div className="w-4 h-4 border-2 border-green-400 border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <div className="w-4 h-4 border border-gray-600 rounded-full" />
                          )}
                          <span className={index <= currentStep ? 'text-green-400' : 'text-gray-500'}>
                            {step}
                          </span>
                        </div>
                        
                        {index === currentStep && isPlaying && (
                          <div className="ml-6 mt-1 text-yellow-400 text-xs">
                            Processing...
                          </div>
                        )}
                      </div>
                    ))}
                    
                    {!isPlaying && currentStep === 0 && (
                      <div className="text-gray-500 mt-4">
                        Click "Start Demo" to begin the {currentScenario.title.toLowerCase()} demonstration
                      </div>
                    )}
                    
                    {!isPlaying && currentStep > 0 && (
                      <div className="text-green-400 mt-4">
                        ✓ Demo completed successfully!
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Demo Description */}
              <Card>
                <CardContent className="p-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                    What you're seeing:
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {currentScenario.description}
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Link href="/dashboard">
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                        Try it yourself
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href="/workspace-ai">
                      <Button variant="outline">
                        <Bot className="mr-2 h-4 w-4" />
                        Explore AI Features
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Feature Highlights */}
          <div className="mt-16 grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="p-6">
                <Zap className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Lightning Fast
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Sub-30 second boot times for instant productivity
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Bot className="h-12 w-12 text-purple-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  AI-Powered
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Intelligent automation and assistance for your workflows
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Terminal className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Full Control
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Complete desktop environment with all your favorite tools
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
