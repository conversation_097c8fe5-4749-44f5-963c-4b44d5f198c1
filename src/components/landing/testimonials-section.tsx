'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Star, 
  Quote, 
  ArrowLeft, 
  ArrowRight,
  Users,
  Building,
  GraduationCap,
  Code,
  Zap,
  Shield
} from 'lucide-react';

const testimonials = [
  {
    id: 1,
    quote: "Omnispace has revolutionized our development workflow. The ability to spin up consistent environments in seconds has eliminated the 'works on my machine' problem entirely.",
    author: "<PERSON>",
    role: "Lead Developer",
    company: "TechCorp",
    avatar: "SC",
    rating: 5,
    category: "Development",
    metrics: "90% faster onboarding"
  },
  {
    id: 2,
    quote: "The security and compliance features give us confidence to use Omnispace for our most sensitive financial applications. The VM isolation is exactly what we needed.",
    author: "<PERSON>",
    role: "CTO",
    company: "FinanceSecure",
    avatar: "MR",
    rating: 5,
    category: "Enterprise",
    metrics: "Zero security incidents"
  },
  {
    id: 3,
    quote: "Our students can focus on learning to code instead of struggling with environment setup. Omnispace has transformed our computer science curriculum.",
    author: "Dr. <PERSON>",
    role: "CS Professor",
    company: "State University",
    avatar: "E<PERSON>",
    rating: 5,
    category: "Education",
    metrics: "100+ students supported"
  },
  {
    id: 4,
    quote: "The AI integration is game-changing. Having AI agents that can actually interact with our development environments opens up incredible automation possibilities.",
    author: "Alex Kumar",
    role: "DevOps Engineer",
    company: "CloudNative Inc",
    avatar: "AK",
    rating: 5,
    category: "AI/Automation",
    metrics: "75% reduction in manual tasks"
  },
  {
    id: 5,
    quote: "Scaling our development team globally was seamless with Omnispace. Everyone has the same powerful environment regardless of their location or device.",
    author: "Lisa Park",
    role: "Engineering Manager",
    company: "GlobalTech",
    avatar: "LP",
    rating: 5,
    category: "Remote Teams",
    metrics: "50% faster team scaling"
  },
  {
    id: 6,
    quote: "The performance is incredible. Sub-30 second boot times and the responsiveness feels like working on a local machine. Our productivity has skyrocketed.",
    author: "David Thompson",
    role: "Senior Developer",
    company: "StartupXYZ",
    avatar: "DT",
    rating: 5,
    category: "Performance",
    metrics: "3x productivity increase"
  }
];

const companies = [
  { name: "TechCorp", logo: "TC", industry: "Technology" },
  { name: "FinanceSecure", logo: "FS", industry: "Financial Services" },
  { name: "State University", logo: "SU", industry: "Education" },
  { name: "CloudNative Inc", logo: "CN", industry: "Cloud Services" },
  { name: "GlobalTech", logo: "GT", industry: "Enterprise Software" },
  { name: "StartupXYZ", logo: "SX", industry: "Startup" },
  { name: "DevCorp", logo: "DC", industry: "Development Tools" },
  { name: "EduTech", logo: "ET", industry: "EdTech" }
];

const stats = [
  { label: "Customer Satisfaction", value: "98%", icon: Star },
  { label: "Enterprise Customers", value: "500+", icon: Building },
  { label: "Active Developers", value: "50k+", icon: Code },
  { label: "Uptime SLA", value: "99.9%", icon: Shield }
];

export function TestimonialsSection() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-rotate testimonials
  useEffect(() => {
    if (isAutoPlaying) {
      const interval = setInterval(() => {
        setCurrentTestimonial(prev => (prev + 1) % testimonials.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [isAutoPlaying]);

  const nextTestimonial = () => {
    setCurrentTestimonial(prev => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial(prev => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const currentTest = testimonials[currentTestimonial];

  return (
    <section className="py-24 bg-gray-50 dark:bg-gray-900/50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">
              <Users className="h-4 w-4 mr-2" />
              Customer Stories
            </Badge>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Trusted by developers
              <span className="block text-blue-600 dark:text-blue-400">
                worldwide
              </span>
            </h2>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Join thousands of developers and teams who have transformed their 
              development workflow with Omnispace.
            </p>
          </div>

          {/* Stats */}
          <div className="grid md:grid-cols-4 gap-8 mb-16">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <stat.icon className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {stat.label}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Featured Testimonial */}
          <div className="mb-16">
            <Card className="relative overflow-hidden">
              <CardContent className="p-8 md:p-12">
                <div className="grid lg:grid-cols-3 gap-8 items-center">
                  {/* Quote */}
                  <div className="lg:col-span-2">
                    <Quote className="h-12 w-12 text-blue-600 mb-6" />
                    <blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                      "{currentTest.quote}"
                    </blockquote>
                    
                    <div className="flex items-center gap-4 mb-4">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                        {currentTest.avatar}
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white">
                          {currentTest.author}
                        </div>
                        <div className="text-gray-600 dark:text-gray-400">
                          {currentTest.role} at {currentTest.company}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        {[...Array(currentTest.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400">
                        <Zap className="h-3 w-3 mr-1" />
                        {currentTest.metrics}
                      </Badge>
                    </div>
                  </div>

                  {/* Navigation */}
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {currentTestimonial + 1} of {testimonials.length}
                      </span>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={prevTestimonial}>
                          <ArrowLeft className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={nextTestimonial}>
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    {/* Testimonial Dots */}
                    <div className="flex flex-wrap gap-2">
                      {testimonials.map((_, index) => (
                        <button
                          key={index}
                          className={`w-3 h-3 rounded-full transition-colors ${
                            index === currentTestimonial 
                              ? 'bg-blue-600' 
                              : 'bg-gray-300 dark:bg-gray-600'
                          }`}
                          onClick={() => {
                            setCurrentTestimonial(index);
                            setIsAutoPlaying(false);
                          }}
                        />
                      ))}
                    </div>
                    
                    <Badge variant="outline" className="w-fit">
                      {currentTest.category}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Company Logos */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-8">
              Trusted by leading companies and institutions
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center opacity-60">
              {companies.map((company, index) => (
                <div key={index} className="flex flex-col items-center gap-2">
                  <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center text-gray-600 dark:text-gray-300 font-bold text-sm">
                    {company.logo}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    {company.name}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Join the Omnispace community
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Start your free trial today and experience the future of development
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                Start Free Trial
              </Button>
              <Button variant="outline" className="px-8 py-3">
                Schedule Demo
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
