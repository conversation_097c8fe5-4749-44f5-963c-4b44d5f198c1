'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Layers, 
  Monitor, 
  Cloud, 
  Database, 
  Globe, 
  Shield,
  ArrowRight,
  ArrowDown,
  Cpu,
  HardDrive,
  Network,
  Bot,
  Terminal,
  Code2
} from 'lucide-react';

const architectureLayers = [
  {
    id: 'frontend',
    title: 'Frontend Layer',
    description: 'Modern web interface built with Next.js and TypeScript',
    icon: Globe,
    color: 'blue',
    technologies: [
      { name: 'Next.js 15', description: 'React framework with App Router' },
      { name: 'TypeScript', description: 'Type-safe development' },
      { name: 'Tailwind CSS', description: 'Utility-first styling' },
      { name: 'shadcn/ui', description: 'Component library' },
      { name: 'noVNC', description: 'Browser-based VNC client' },
    ],
  },
  {
    id: 'api',
    title: 'API & WebSocket Layer',
    description: 'RESTful APIs and real-time WebSocket connections',
    icon: Network,
    color: 'green',
    technologies: [
      { name: 'REST API', description: 'VM management endpoints' },
      { name: 'WebSocket', description: 'Real-time VNC connections' },
      { name: 'AI SDK V5', description: 'AI integration and tool calling' },
      { name: 'Authentication', description: 'Secure access control' },
    ],
  },
  {
    id: 'virtualization',
    title: 'Virtualization Layer',
    description: 'Lightweight MicroVMs with container orchestration',
    icon: Layers,
    color: 'purple',
    technologies: [
      { name: 'Firecracker VMM', description: 'Lightweight virtualization' },
      { name: 'Docker', description: 'Container orchestration' },
      { name: 'Ubuntu Containers', description: 'Development environments' },
      { name: 'VNC Server', description: 'Remote desktop access' },
    ],
  },
  {
    id: 'infrastructure',
    title: 'Infrastructure Layer',
    description: 'Scalable cloud infrastructure and security',
    icon: Cloud,
    color: 'red',
    technologies: [
      { name: 'Linux Host', description: 'Optimized for Firecracker' },
      { name: 'Resource Management', description: 'CPU, memory, storage' },
      { name: 'Network Security', description: 'Isolated networking' },
      { name: 'Monitoring', description: 'Performance and health' },
    ],
  },
];

const dataFlow = [
  {
    step: 1,
    title: 'User Access',
    description: 'User accesses Omnispace through web browser',
    icon: Globe,
  },
  {
    step: 2,
    title: 'VM Request',
    description: 'Frontend requests VM creation or connection',
    icon: Monitor,
  },
  {
    step: 3,
    title: 'Firecracker Launch',
    description: 'Firecracker creates lightweight MicroVM',
    icon: Cpu,
  },
  {
    step: 4,
    title: 'Container Start',
    description: 'Ubuntu container starts inside MicroVM',
    icon: Code2,
  },
  {
    step: 5,
    title: 'VNC Connection',
    description: 'VNC server enables remote desktop access',
    icon: Terminal,
  },
  {
    step: 6,
    title: 'AI Integration',
    description: 'AI agents can interact with the workspace',
    icon: Bot,
  },
];

const getColorClasses = (color: string) => {
  const colors = {
    blue: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400',
    green: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400',
    purple: 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800 text-purple-600 dark:text-purple-400',
    red: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-600 dark:text-red-400',
  };
  return colors[color as keyof typeof colors] || colors.blue;
};

export function ArchitectureSection() {
  const [selectedLayer, setSelectedLayer] = useState('frontend');

  return (
    <section id="architecture" className="py-24 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">
              <Layers className="h-4 w-4 mr-2" />
              Architecture
            </Badge>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Built for
              <span className="block text-purple-600 dark:text-purple-400">
                Scale & Performance
              </span>
            </h2>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Omnispace's architecture combines modern web technologies with cutting-edge 
              virtualization to deliver secure, scalable, and high-performance remote workspaces.
            </p>
          </div>

          <Tabs value={selectedLayer} onValueChange={setSelectedLayer} className="space-y-8">
            {/* Architecture Overview */}
            <div className="grid lg:grid-cols-2 gap-12 mb-12">
              {/* Visual Architecture */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  System Architecture
                </h3>
                
                <div className="space-y-4">
                  {architectureLayers.map((layer, index) => (
                    <div key={layer.id} className="relative">
                      <Card 
                        className={`cursor-pointer transition-all duration-300 ${
                          selectedLayer === layer.id 
                            ? 'ring-2 ring-blue-500 shadow-lg' 
                            : 'hover:shadow-md'
                        }`}
                        onClick={() => setSelectedLayer(layer.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg border ${getColorClasses(layer.color)}`}>
                              <layer.icon className="h-5 w-5" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                {layer.title}
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                {layer.description}
                              </p>
                            </div>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                          </div>
                        </CardContent>
                      </Card>
                      
                      {index < architectureLayers.length - 1 && (
                        <div className="flex justify-center py-2">
                          <ArrowDown className="h-4 w-4 text-gray-400" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Data Flow */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Data Flow
                </h3>
                
                <div className="space-y-4">
                  {dataFlow.map((step, index) => (
                    <div key={step.step} className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {step.step}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <step.icon className="h-4 w-4 text-blue-600" />
                          <h4 className="font-semibold text-gray-900 dark:text-white">
                            {step.title}
                          </h4>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {step.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Layer Details */}
            <TabsList className="grid w-full grid-cols-4">
              {architectureLayers.map((layer) => (
                <TabsTrigger key={layer.id} value={layer.id} className="text-sm">
                  <layer.icon className="h-4 w-4 mr-2" />
                  {layer.title.split(' ')[0]}
                </TabsTrigger>
              ))}
            </TabsList>

            {architectureLayers.map((layer) => (
              <TabsContent key={layer.id} value={layer.id}>
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className={`p-3 rounded-lg border ${getColorClasses(layer.color)}`}>
                        <layer.icon className="h-6 w-6" />
                      </div>
                      <div>
                        <CardTitle className="text-2xl">{layer.title}</CardTitle>
                        <p className="text-gray-600 dark:text-gray-300">
                          {layer.description}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-6">
                      {layer.technologies.map((tech, index) => (
                        <div key={index} className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                          <div>
                            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                              {tech.name}
                            </h4>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {tech.description}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>

          {/* Technical Specifications */}
          <div className="mt-16 grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="p-6">
                <Cpu className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  High Performance
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Sub-second VM boot times with minimal resource overhead
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Shield className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Enterprise Security
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  VM-level isolation with encrypted connections and access controls
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <HardDrive className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Scalable Infrastructure
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Auto-scaling architecture for single-user to enterprise deployments
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
