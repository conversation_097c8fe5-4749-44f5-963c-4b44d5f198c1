'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight, 
  Play, 
  Cloud, 
  Zap, 
  Shield, 
  Bot,
  Monitor,
  Cpu,
  Globe
} from 'lucide-react';

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900" />
      
      {/* Animated Background Grid */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-20 w-24 h-24 bg-green-500/10 rounded-full blur-xl animate-pulse delay-2000" />
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-yellow-500/10 rounded-full blur-xl animate-pulse delay-3000" />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-8">
            {/* Announcement Badge */}
            <div className="flex justify-center">
              <Badge variant="outline" className="px-4 py-2 text-sm bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                <Zap className="h-4 w-4 mr-2 text-blue-600" />
                Now with AI-Powered Computer Use
              </Badge>
            </div>

            {/* Main Headline */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white leading-tight">
                The Future of
                <span className="block bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                  Remote Workspaces
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
                Cloud-based development environments powered by{' '}
                <span className="font-semibold text-blue-600 dark:text-blue-400">Firecracker MicroVMs</span>,{' '}
                seamless VNC access, and intelligent{' '}
                <span className="font-semibold text-purple-600 dark:text-purple-400">AI assistance</span>.
              </p>
            </div>

            {/* Feature Highlights */}
            <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-2">
                <Cloud className="h-5 w-5 text-blue-500" />
                <span>Lightweight MicroVMs</span>
              </div>
              <div className="flex items-center gap-2">
                <Monitor className="h-5 w-5 text-green-500" />
                <span>Browser-based VNC</span>
              </div>
              <div className="flex items-center gap-2">
                <Bot className="h-5 w-5 text-purple-500" />
                <span>AI-Powered Automation</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-red-500" />
                <span>Enterprise Security</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
              <Link href="/dashboard">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              
              <Link href="#demo">
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg">
                  <Play className="mr-2 h-5 w-5" />
                  Watch Demo
                </Button>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="pt-12">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                Trusted by developers and teams worldwide
              </p>
              
              <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
                {/* Placeholder for company logos */}
                <div className="flex items-center gap-2 text-gray-400">
                  <Globe className="h-6 w-6" />
                  <span className="font-medium">Global Teams</span>
                </div>
                <div className="flex items-center gap-2 text-gray-400">
                  <Cpu className="h-6 w-6" />
                  <span className="font-medium">High Performance</span>
                </div>
                <div className="flex items-center gap-2 text-gray-400">
                  <Shield className="h-6 w-6" />
                  <span className="font-medium">Enterprise Ready</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white dark:from-gray-900 to-transparent" />
    </section>
  );
}
