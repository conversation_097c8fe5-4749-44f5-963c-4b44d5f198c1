'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { 
  Check, 
  X, 
  Star, 
  Zap, 
  Shield, 
  Users,
  Building,
  Crown,
  ArrowRight,
  HelpCircle
} from 'lucide-react';

const pricingPlans = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for individual developers and small projects',
    icon: Zap,
    color: 'blue',
    popular: false,
    pricing: {
      monthly: 0,
      yearly: 0
    },
    limits: {
      vms: '2 concurrent VMs',
      storage: '10 GB storage',
      hours: '50 hours/month',
      support: 'Community support'
    },
    features: [
      'Browser-based VNC access',
      'Ubuntu development environments',
      'Basic VM configurations',
      'Community support',
      'Standard performance',
      'Public cloud hosting'
    ],
    notIncluded: [
      'AI-powered automation',
      'Custom VM images',
      'Priority support',
      'Advanced security features',
      'Team collaboration tools',
      'Enterprise integrations'
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'Ideal for professional developers and small teams',
    icon: Users,
    color: 'green',
    popular: true,
    pricing: {
      monthly: 29,
      yearly: 290
    },
    limits: {
      vms: '10 concurrent VMs',
      storage: '100 GB storage',
      hours: 'Unlimited hours',
      support: 'Email support'
    },
    features: [
      'Everything in Starter',
      'AI-powered computer use',
      'Advanced VM configurations',
      'Custom development images',
      'Priority performance',
      'Email support',
      'Team collaboration tools',
      'Version control integration',
      'Automated backups'
    ],
    notIncluded: [
      'Enterprise security features',
      'Dedicated infrastructure',
      'Custom integrations',
      'SLA guarantees',
      'Advanced compliance'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For large teams and organizations with advanced needs',
    icon: Building,
    color: 'purple',
    popular: false,
    pricing: {
      monthly: 99,
      yearly: 990
    },
    limits: {
      vms: 'Unlimited VMs',
      storage: '1 TB+ storage',
      hours: 'Unlimited hours',
      support: '24/7 priority support'
    },
    features: [
      'Everything in Professional',
      'Enterprise security & compliance',
      'Dedicated infrastructure',
      'Custom integrations',
      'Advanced user management',
      'Audit trails & logging',
      'SLA guarantees (99.9%)',
      'On-premise deployment options',
      'Custom training & onboarding',
      'Dedicated account manager'
    ],
    notIncluded: []
  }
];

const enterpriseFeatures = [
  'SOC 2 Type II compliance',
  'SAML/SSO integration',
  'Advanced RBAC',
  'Custom VM images',
  'Dedicated support team',
  'Custom SLA agreements',
  'On-premise deployment',
  'Advanced monitoring & analytics'
];

const getColorClasses = (color: string) => {
  const colors = {
    blue: {
      bg: 'bg-blue-50 dark:bg-blue-900/20',
      border: 'border-blue-200 dark:border-blue-800',
      text: 'text-blue-600 dark:text-blue-400',
      button: 'bg-blue-600 hover:bg-blue-700'
    },
    green: {
      bg: 'bg-green-50 dark:bg-green-900/20',
      border: 'border-green-200 dark:border-green-800',
      text: 'text-green-600 dark:text-green-400',
      button: 'bg-green-600 hover:bg-green-700'
    },
    purple: {
      bg: 'bg-purple-50 dark:bg-purple-900/20',
      border: 'border-purple-200 dark:border-purple-800',
      text: 'text-purple-600 dark:text-purple-400',
      button: 'bg-purple-600 hover:bg-purple-700'
    }
  };
  return colors[color as keyof typeof colors] || colors.blue;
};

export function PricingSection() {
  const [isYearly, setIsYearly] = useState(false);

  const getPrice = (plan: typeof pricingPlans[0]) => {
    if (plan.pricing.monthly === 0) return 'Free';
    const price = isYearly ? plan.pricing.yearly : plan.pricing.monthly;
    const period = isYearly ? 'year' : 'month';
    const savings = isYearly ? Math.round(((plan.pricing.monthly * 12 - plan.pricing.yearly) / (plan.pricing.monthly * 12)) * 100) : 0;
    
    return (
      <div>
        <span className="text-4xl font-bold">${price}</span>
        <span className="text-gray-600 dark:text-gray-400">/{period}</span>
        {isYearly && savings > 0 && (
          <div className="text-sm text-green-600 dark:text-green-400">
            Save {savings}%
          </div>
        )}
      </div>
    );
  };

  return (
    <section id="pricing" className="py-24 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">
              <Crown className="h-4 w-4 mr-2" />
              Pricing Plans
            </Badge>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Choose the perfect
              <span className="block text-blue-600 dark:text-blue-400">
                plan for your needs
              </span>
            </h2>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Start free and scale as you grow. All plans include our core features 
              with transparent, usage-based pricing.
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center gap-4">
              <span className={`text-sm ${!isYearly ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-500'}`}>
                Monthly
              </span>
              <Switch
                checked={isYearly}
                onCheckedChange={setIsYearly}
              />
              <span className={`text-sm ${isYearly ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-500'}`}>
                Yearly
              </span>
              <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400">
                Save up to 17%
              </Badge>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            {pricingPlans.map((plan) => {
              const colorClasses = getColorClasses(plan.color);
              
              return (
                <Card 
                  key={plan.id}
                  className={`relative ${
                    plan.popular 
                      ? 'ring-2 ring-blue-500 shadow-xl scale-105' 
                      : 'shadow-lg hover:shadow-xl transition-shadow'
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-600 text-white px-4 py-1">
                        <Star className="h-3 w-3 mr-1" />
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardHeader className="text-center pb-8">
                    <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${colorClasses.bg} ${colorClasses.border} border mb-4 mx-auto`}>
                      <plan.icon className={`h-6 w-6 ${colorClasses.text}`} />
                    </div>
                    
                    <CardTitle className="text-2xl text-gray-900 dark:text-white">
                      {plan.name}
                    </CardTitle>
                    
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {plan.description}
                    </p>
                    
                    <div className="pt-4">
                      {getPrice(plan)}
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-6">
                    {/* Limits */}
                    <div className="space-y-2">
                      <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                        What's included:
                      </h4>
                      <div className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                        <div>• {plan.limits.vms}</div>
                        <div>• {plan.limits.storage}</div>
                        <div>• {plan.limits.hours}</div>
                        <div>• {plan.limits.support}</div>
                      </div>
                    </div>
                    
                    {/* Features */}
                    <div className="space-y-3">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {feature}
                          </span>
                        </div>
                      ))}
                      
                      {plan.notIncluded.map((feature, index) => (
                        <div key={index} className="flex items-start gap-3 opacity-50">
                          <X className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-500 line-through">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                    
                    {/* CTA Button */}
                    <Button 
                      className={`w-full ${
                        plan.popular 
                          ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                          : `${colorClasses.button} text-white`
                      }`}
                    >
                      {plan.id === 'starter' ? 'Get Started Free' : 'Start Free Trial'}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                    
                    {plan.id === 'enterprise' && (
                      <Button variant="outline" className="w-full">
                        Contact Sales
                      </Button>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Enterprise Features */}
          <Card className="mb-16">
            <CardHeader>
              <CardTitle className="text-center text-2xl text-gray-900 dark:text-white">
                Enterprise Features
              </CardTitle>
              <p className="text-center text-gray-600 dark:text-gray-300">
                Additional features available for Enterprise customers
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {enterpriseFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Shield className="h-4 w-4 text-purple-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {feature}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* FAQ */}
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Questions about pricing?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our team is here to help you find the right plan for your needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" className="px-8 py-3">
                <HelpCircle className="mr-2 h-4 w-4" />
                View FAQ
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                Contact Sales
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
