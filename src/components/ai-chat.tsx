'use client';

import { useChat } from '@ai-sdk/react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Loader2, Send, User, Bot, Wrench, Calculator, CloudSun } from 'lucide-react';

// Helper function to get tool icon
const getToolIcon = (toolName: string) => {
  switch (toolName) {
    case 'weather':
      return <CloudSun className="h-4 w-4" />;
    case 'calculator':
      return <Calculator className="h-4 w-4" />;
    case 'convertFahrenheitToCelsius':
      return <Calculator className="h-4 w-4" />;
    default:
      return <Wrench className="h-4 w-4" />;
  }
};

// Helper function to render tool calls
const renderToolCall = (part: any, messageId: string, partIndex: number) => {
  const toolName = part.type.replace('tool-', '');

  return (
    <div key={`${messageId}-${partIndex}`} className="mt-2 p-3 bg-muted rounded-lg border">
      <div className="flex items-center gap-2 mb-2">
        {getToolIcon(toolName)}
        <Badge variant="secondary" className="text-xs">
          {toolName}
        </Badge>
      </div>

      {part.args && (
        <div className="text-sm text-muted-foreground mb-2">
          <strong>Input:</strong> {JSON.stringify(part.args, null, 2)}
        </div>
      )}

      {part.result && (
        <div className="text-sm">
          <strong>Result:</strong>
          <pre className="mt-1 p-2 bg-background rounded text-xs overflow-x-auto">
            {JSON.stringify(part.result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default function AIChat() {
  const [input, setInput] = useState('');
  const { messages, sendMessage, isLoading } = useChat();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;
    
    sendMessage({ text: input });
    setInput('');
  };

  return (
    <Card className="w-full max-w-4xl mx-auto h-[600px] flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          AI Assistant
        </CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col gap-4">
        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-4">
            {messages.length === 0 && (
              <div className="text-center text-muted-foreground py-8">
                Start a conversation with the AI assistant
              </div>
            )}
            
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`flex gap-3 max-w-[80%] ${
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {message.role === 'user' ? (
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-primary-foreground" />
                      </div>
                    ) : (
                      <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                        <Bot className="h-4 w-4 text-secondary-foreground" />
                      </div>
                    )}
                  </div>
                  
                  <div
                    className={`rounded-lg px-4 py-2 ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-secondary text-secondary-foreground'
                    }`}
                  >
                    {message.parts.map((part, i) => {
                      switch (part.type) {
                        case 'text':
                          return (
                            <div key={`${message.id}-${i}`} className="whitespace-pre-wrap">
                              {part.text}
                            </div>
                          );
                        case 'tool-weather':
                        case 'tool-calculator':
                        case 'tool-convertFahrenheitToCelsius':
                          return renderToolCall(part, message.id, i);
                        default:
                          // Handle any other tool types generically
                          if (part.type.startsWith('tool-')) {
                            return renderToolCall(part, message.id, i);
                          }
                          return null;
                      }
                    })}
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="flex gap-3 max-w-[80%]">
                  <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                    <Bot className="h-4 w-4 text-secondary-foreground" />
                  </div>
                  <div className="bg-secondary text-secondary-foreground rounded-lg px-4 py-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
        
        <form onSubmit={handleSubmit} className="flex gap-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type your message..."
            disabled={isLoading}
            className="flex-1"
          />
          <Button type="submit" disabled={isLoading || !input.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
