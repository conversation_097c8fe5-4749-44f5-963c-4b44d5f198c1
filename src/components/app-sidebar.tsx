"use client"

import * as React from "react"
import { Cloud, Monitor, Plus, Settings, Activity, HardDrive, Cpu } from "lucide-react"

import { NavUser } from "@/components/nav-user"
import { Label } from "@/components/ui/label"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { Switch } from "@/components/ui/switch"

// Omnispace data
const data = {
  user: {
    name: "Admin User",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: Monitor,
      isActive: true,
    },
    {
      title: "VMs",
      url: "/dashboard/vms",
      icon: HardDrive,
      isActive: false,
    },
    {
      title: "Create VM",
      url: "/dashboard/create",
      icon: Plus,
      isActive: false,
    },
    {
      title: "Resources",
      url: "/dashboard/resources",
      icon: Cpu,
      isActive: false,
    },
    {
      title: "Activity",
      url: "/dashboard/activity",
      icon: Activity,
      isActive: false,
    },
    {
      title: "Settings",
      url: "/dashboard/settings",
      icon: Settings,
      isActive: false,
    },
  ],
  vms: [
    {
      id: "vm-1",
      name: "Ubuntu Desktop",
      status: "running",
      cpu: 2,
      memory: 2048,
      vncPort: 5901,
      lastAccessed: "2 mins ago",
      description: "Primary development environment with GNOME desktop",
    },
    {
      id: "vm-2",
      name: "Development VM",
      status: "stopped",
      cpu: 4,
      memory: 4096,
      vncPort: 5902,
      lastAccessed: "1 hour ago",
      description: "High-performance VM for heavy development tasks",
    },
    {
      id: "vm-3",
      name: "Test Environment",
      status: "running",
      cpu: 1,
      memory: 1024,
      vncPort: 5903,
      lastAccessed: "30 mins ago",
      description: "Lightweight testing environment",
    },
    {
      id: "vm-4",
      name: "Windows VM",
      status: "starting",
      cpu: 4,
      memory: 8192,
      vncPort: 5904,
      lastAccessed: "Never",
      description: "Windows 11 virtual machine for cross-platform testing",
    },
    {
      id: "vm-5",
      name: "Alpine Linux",
      status: "stopped",
      cpu: 1,
      memory: 512,
      vncPort: 5905,
      lastAccessed: "2 days ago",
      description: "Minimal Alpine Linux for container development",
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  // Note: I'm using state to show active item.
  // IRL you should use the url/router.
  const [activeItem, setActiveItem] = React.useState(data.navMain[0])
  const [vms, setVMs] = React.useState(data.vms)
  const { setOpen } = useSidebar()

  return (
    <Sidebar
      collapsible="icon"
      className="overflow-hidden *:data-[sidebar=sidebar]:flex-row"
      {...props}
    >
      {/* This is the first sidebar */}
      {/* We disable collapsible and adjust width to icon. */}
      {/* This will make the sidebar appear as icons. */}
      <Sidebar
        collapsible="none"
        className="w-[calc(var(--sidebar-width-icon)+1px)]! border-r"
      >
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild className="md:h-8 md:p-0">
                <a href="#">
                  <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                    <Cloud className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">Omnispace</span>
                    <span className="truncate text-xs">VM Platform</span>
                  </div>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent className="px-1.5 md:px-0">
              <SidebarMenu>
                {data.navMain.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      tooltip={{
                        children: item.title,
                        hidden: false,
                      }}
                      onClick={() => {
                        setActiveItem(item)
                        const shuffledVMs = data.vms.sort(() => Math.random() - 0.5)
                        setVMs(
                          shuffledVMs.slice(
                            0,
                            Math.max(3, Math.floor(Math.random() * 5) + 1)
                          )
                        )
                        setOpen(true)
                      }}
                      isActive={activeItem?.title === item.title}
                      className="px-2.5 md:px-2"
                    >
                      <item.icon />
                      <span>{item.title}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <NavUser user={data.user} />
        </SidebarFooter>
      </Sidebar>

      {/* This is the second sidebar */}
      {/* We disable collapsible and let it fill remaining space */}
      <Sidebar collapsible="none" className="hidden flex-1 md:flex">
        <SidebarHeader className="gap-3.5 border-b p-4">
          <div className="flex w-full items-center justify-between">
            <div className="text-foreground text-base font-medium">
              {activeItem?.title}
            </div>
            <Label className="flex items-center gap-2 text-sm">
              <span>Running</span>
              <Switch className="shadow-none" />
            </Label>
          </div>
          <SidebarInput placeholder="Search VMs..." />
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup className="px-0">
            <SidebarGroupContent>
              {vms.map((vm) => {
                const getStatusColor = (status: string) => {
                  switch (status) {
                    case 'running': return 'text-green-600 bg-green-100';
                    case 'stopped': return 'text-red-600 bg-red-100';
                    case 'starting': return 'text-yellow-600 bg-yellow-100';
                    case 'stopping': return 'text-orange-600 bg-orange-100';
                    default: return 'text-gray-600 bg-gray-100';
                  }
                };
                
                return (
                  <a
                    href="#"
                    key={vm.id}
                    className="hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex flex-col items-start gap-2 border-b p-4 text-sm leading-tight whitespace-nowrap last:border-b-0"
                  >
                    <div className="flex w-full items-center gap-2">
                      <span className="font-medium">{vm.name}</span>
                      <span className={`ml-auto text-xs px-2 py-1 rounded-full ${getStatusColor(vm.status)}`}>
                        {vm.status}
                      </span>
                    </div>
                    <div className="flex w-full items-center gap-4 text-xs text-gray-600">
                      <span>{vm.cpu} CPU</span>
                      <span>{vm.memory}MB</span>
                      <span>:{vm.vncPort}</span>
                    </div>
                    <span className="line-clamp-2 w-[260px] text-xs text-gray-500">
                      {vm.description}
                    </span>
                    <span className="text-xs text-gray-400">
                      Last accessed: {vm.lastAccessed}
                    </span>
                  </a>
                );
              })}
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </Sidebar>
  )
}
