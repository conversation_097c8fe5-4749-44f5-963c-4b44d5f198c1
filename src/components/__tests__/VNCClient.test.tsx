import React from 'react';
import { render, screen } from '@testing-library/react';
import { VNCClient } from '../VNCClient';

// Mock the noVNC module
jest.mock('@novnc/novnc/lib/rfb', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      disconnect: jest.fn(),
      addEventListener: jest.fn(),
      scaleViewport: true,
      resizeSession: true,
    })),
  };
});

describe('VNCClient', () => {
  const defaultProps = {
    url: 'ws://localhost:6080',
    vmName: 'Test VM',
  };

  it('renders without crashing', () => {
    render(<VNCClient {...defaultProps} />);
    expect(screen.getByText('Test VM')).toBeInTheDocument();
  });

  it('shows connect button initially', () => {
    render(<VNCClient {...defaultProps} />);
    expect(screen.getByText('Connect')).toBeInTheDocument();
  });

  it('displays the VM name in the header', () => {
    render(<VNCClient {...defaultProps} />);
    expect(screen.getByText('Test VM')).toBeInTheDocument();
  });
});
