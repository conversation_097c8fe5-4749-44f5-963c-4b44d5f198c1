'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Plus, 
  Play, 
  Square, 
  Trash2, 
  Monitor, 
  Cpu, 
  HardDrive,
  Activity,
  Clock
} from 'lucide-react';

interface MicroVM {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'starting' | 'stopping';
  cpu: number;
  memory: number; // in MB
  diskSize: number; // in GB
  vncPort: number;
  createdAt: Date;
  lastAccessed?: Date;
}

interface VMManagerProps {
  onSelectVM: (vm: MicroVM) => void;
}

export const VMManager: React.FC<VMManagerProps> = ({ onSelectVM }) => {
  const [vms, setVms] = useState<MicroVM[]>([
    {
      id: '1',
      name: 'Ubuntu Desktop',
      status: 'running',
      cpu: 2,
      memory: 2048,
      diskSize: 20,
      vncPort: 5901,
      createdAt: new Date('2024-01-15'),
      lastAccessed: new Date()
    },
    {
      id: '2',
      name: 'Development VM',
      status: 'stopped',
      cpu: 4,
      memory: 4096,
      diskSize: 40,
      vncPort: 5902,
      createdAt: new Date('2024-01-10')
    }
  ]);

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newVM, setNewVM] = useState({
    name: '',
    cpu: 2,
    memory: 2048,
    diskSize: 20
  });

  const createVM = async () => {
    const vm: MicroVM = {
      id: Date.now().toString(),
      name: newVM.name,
      status: 'starting',
      cpu: newVM.cpu,
      memory: newVM.memory,
      diskSize: newVM.diskSize,
      vncPort: 5900 + vms.length + 1,
      createdAt: new Date()
    };

    setVms(prev => [...prev, vm]);
    setIsCreateDialogOpen(false);
    setNewVM({ name: '', cpu: 2, memory: 2048, diskSize: 20 });

    // Simulate VM creation process
    setTimeout(() => {
      setVms(prev => prev.map(v => 
        v.id === vm.id ? { ...v, status: 'running' as const } : v
      ));
    }, 3000);
  };

  const startVM = async (id: string) => {
    setVms(prev => prev.map(vm => 
      vm.id === id ? { ...vm, status: 'starting' } : vm
    ));

    // Simulate start process
    setTimeout(() => {
      setVms(prev => prev.map(vm => 
        vm.id === id ? { ...vm, status: 'running' } : vm
      ));
    }, 2000);
  };

  const stopVM = async (id: string) => {
    setVms(prev => prev.map(vm => 
      vm.id === id ? { ...vm, status: 'stopping' } : vm
    ));

    // Simulate stop process
    setTimeout(() => {
      setVms(prev => prev.map(vm => 
        vm.id === id ? { ...vm, status: 'stopped' } : vm
      ));
    }, 1500);
  };

  const deleteVM = async (id: string) => {
    setVms(prev => prev.filter(vm => vm.id !== id));
  };

  const connectToVM = (vm: MicroVM) => {
    if (vm.status === 'running') {
      setVms(prev => prev.map(v => 
        v.id === vm.id ? { ...v, lastAccessed: new Date() } : v
      ));
      onSelectVM(vm);
    }
  };

  const getStatusColor = (status: MicroVM['status']) => {
    switch (status) {
      case 'running': return 'text-green-600';
      case 'stopped': return 'text-red-600';
      case 'starting': return 'text-yellow-600';
      case 'stopping': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">MicroVM Management</h2>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create VM
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New MicroVM</DialogTitle>
              <DialogDescription>
                Configure your new Firecracker microVM with custom specifications.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newVM.name}
                  onChange={(e) => setNewVM(prev => ({ ...prev, name: e.target.value }))}
                  className="col-span-3"
                  placeholder="My VM"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="cpu" className="text-right">
                  CPU Cores
                </Label>
                <Input
                  id="cpu"
                  type="number"
                  min="1"
                  max="8"
                  value={newVM.cpu}
                  onChange={(e) => setNewVM(prev => ({ ...prev, cpu: parseInt(e.target.value) }))}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="memory" className="text-right">
                  Memory (MB)
                </Label>
                <Input
                  id="memory"
                  type="number"
                  min="512"
                  step="512"
                  value={newVM.memory}
                  onChange={(e) => setNewVM(prev => ({ ...prev, memory: parseInt(e.target.value) }))}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="disk" className="text-right">
                  Disk Size (GB)
                </Label>
                <Input
                  id="disk"
                  type="number"
                  min="10"
                  step="5"
                  value={newVM.diskSize}
                  onChange={(e) => setNewVM(prev => ({ ...prev, diskSize: parseInt(e.target.value) }))}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="submit" 
                onClick={createVM}
                disabled={!newVM.name.trim()}
              >
                Create VM
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {vms.map((vm) => (
          <Card key={vm.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{vm.name}</CardTitle>
                <div className={`flex items-center gap-1 ${getStatusColor(vm.status)}`}>
                  <Activity className="h-4 w-4" />
                  <span className="text-sm font-medium capitalize">{vm.status}</span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Cpu className="h-4 w-4 text-gray-500" />
                  <span>{vm.cpu} CPU</span>
                </div>
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4 text-gray-500" />
                  <span>{vm.memory}MB RAM</span>
                </div>
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4 text-gray-500" />
                  <span>{vm.diskSize}GB Disk</span>
                </div>
                <div className="flex items-center gap-2">
                  <Monitor className="h-4 w-4 text-gray-500" />
                  <span>:{vm.vncPort}</span>
                </div>
              </div>

              <div className="text-xs text-gray-500 flex items-center gap-2">
                <Clock className="h-3 w-3" />
                Created {vm.createdAt.toLocaleDateString()}
              </div>

              <div className="flex gap-2">
                {vm.status === 'stopped' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => startVM(vm.id)}
                    className="flex-1"
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Start
                  </Button>
                )}
                
                {vm.status === 'running' && (
                  <>
                    <Button
                      size="sm"
                      onClick={() => connectToVM(vm)}
                      className="flex-1"
                    >
                      <Monitor className="h-4 w-4 mr-1" />
                      Connect
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => stopVM(vm.id)}
                    >
                      <Square className="h-4 w-4" />
                    </Button>
                  </>
                )}

                {(vm.status === 'starting' || vm.status === 'stopping') && (
                  <Button size="sm" disabled className="flex-1">
                    {vm.status === 'starting' ? 'Starting...' : 'Stopping...'}
                  </Button>
                )}

                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => deleteVM(vm.id)}
                  disabled={vm.status === 'running'}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
