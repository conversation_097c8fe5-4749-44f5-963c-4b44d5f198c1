'use client';

import { useState } from 'react';
import AI<PERSON><PERSON> from '@/components/ai-chat';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2 } from 'lucide-react';

export default function AIDemoPage() {
  const [objectPrompt, setObjectPrompt] = useState('');
  const [objectType, setObjectType] = useState<'person' | 'recipe'>('person');
  const [objectResult, setObjectResult] = useState<any>(null);
  const [objectLoading, setObjectLoading] = useState(false);

  const [embeddingText, setEmbeddingText] = useState('');
  const [embeddingResult, setEmbeddingResult] = useState<any>(null);
  const [embeddingLoading, setEmbeddingLoading] = useState(false);

  const generateObject = async () => {
    if (!objectPrompt.trim()) return;

    setObjectLoading(true);
    try {
      const response = await fetch('/api/generate-object', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: objectPrompt, type: objectType }),
      });
      const data = await response.json();
      setObjectResult(data);
    } catch (error) {
      console.error('Error generating object:', error);
    } finally {
      setObjectLoading(false);
    }
  };

  const generateEmbedding = async () => {
    if (!embeddingText.trim()) return;

    setEmbeddingLoading(true);
    try {
      const response = await fetch('/api/embeddings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: embeddingText }),
      });
      const data = await response.json();
      setEmbeddingResult(data);
    } catch (error) {
      console.error('Error generating embedding:', error);
    } finally {
      setEmbeddingLoading(false);
    }
  };
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">AI SDK V5 Demo</h1>
          <p className="text-lg text-muted-foreground mb-6">
            Experience the power of AI SDK V5 with streaming chat, tool calling, structured data generation, and embeddings
          </p>
        </div>

        <Tabs defaultValue="chat" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chat">Chat & Tools</TabsTrigger>
            <TabsTrigger value="objects">Structured Data</TabsTrigger>
            <TabsTrigger value="embeddings">Embeddings</TabsTrigger>
          </TabsList>

          <TabsContent value="chat" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="p-4 bg-card rounded-lg border">
                <h3 className="font-semibold mb-2">🌤️ Weather Tool</h3>
                <p className="text-sm text-muted-foreground">
                  Ask about weather in any location
                </p>
              </div>
              <div className="p-4 bg-card rounded-lg border">
                <h3 className="font-semibold mb-2">🧮 Calculator</h3>
                <p className="text-sm text-muted-foreground">
                  Perform mathematical calculations
                </p>
              </div>
              <div className="p-4 bg-card rounded-lg border">
                <h3 className="font-semibold mb-2">🌡️ Temperature Converter</h3>
                <p className="text-sm text-muted-foreground">
                  Convert between Fahrenheit and Celsius
                </p>
              </div>
            </div>

            <AIChat />

            <div className="text-center text-sm text-muted-foreground">
              <p>Try asking: "What's the weather in New York in Celsius?" or "Calculate 15 * 23"</p>
            </div>
          </TabsContent>

          <TabsContent value="objects" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Structured Data Generation</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Generate structured data objects using AI SDK's generateObject function
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Button
                    variant={objectType === 'person' ? 'default' : 'outline'}
                    onClick={() => setObjectType('person')}
                  >
                    Person
                  </Button>
                  <Button
                    variant={objectType === 'recipe' ? 'default' : 'outline'}
                    onClick={() => setObjectType('recipe')}
                  >
                    Recipe
                  </Button>
                </div>

                <div className="space-y-2">
                  <Input
                    placeholder={
                      objectType === 'person'
                        ? "Describe a person (e.g., 'A software engineer from Tokyo who loves hiking')"
                        : "Describe a recipe (e.g., 'A healthy vegetarian pasta dish')"
                    }
                    value={objectPrompt}
                    onChange={(e) => setObjectPrompt(e.target.value)}
                  />
                  <Button onClick={generateObject} disabled={objectLoading || !objectPrompt.trim()}>
                    {objectLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Generate {objectType}
                  </Button>
                </div>

                {objectResult && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Generated {objectType}:</h4>
                    <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                      {JSON.stringify(objectResult.data, null, 2)}
                    </pre>
                    {objectResult.usage && (
                      <div className="mt-2 text-xs text-muted-foreground">
                        Tokens used: {objectResult.usage.totalTokens}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="embeddings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Text Embeddings</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Generate vector embeddings for text using OpenAI's embedding models
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Textarea
                    placeholder="Enter text to generate embeddings for..."
                    value={embeddingText}
                    onChange={(e) => setEmbeddingText(e.target.value)}
                    rows={3}
                  />
                  <Button onClick={generateEmbedding} disabled={embeddingLoading || !embeddingText.trim()}>
                    {embeddingLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Generate Embedding
                  </Button>
                </div>

                {embeddingResult && (
                  <div className="mt-4">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-semibold">Embedding Vector:</h4>
                      <Badge variant="secondary">{embeddingResult.dimensions} dimensions</Badge>
                    </div>
                    <div className="bg-muted p-4 rounded-lg text-sm">
                      <p className="mb-2">First 10 values:</p>
                      <code className="text-xs">
                        [{embeddingResult.embedding.slice(0, 10).map((val: number) => val.toFixed(6)).join(', ')}...]
                      </code>
                    </div>
                    {embeddingResult.usage && (
                      <div className="mt-2 text-xs text-muted-foreground">
                        Tokens used: {embeddingResult.usage.totalTokens}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
