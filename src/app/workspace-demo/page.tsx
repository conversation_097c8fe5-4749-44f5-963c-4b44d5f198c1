'use client';

import React, { useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Code,
  LayoutDashboard,
  Bot,
  Monitor,
  FileText,
  Terminal,
  Search,
  GitBranch
} from 'lucide-react';
import {
  WorkspaceLayout,
  useWorkspace,
  defaultLayouts,
  TabConfig
} from '@/components/workspace';

function WorkspaceDemoContent() {
  const { 
    createLayout, 
    setActiveLayout, 
    addTab,
    layouts, 
    activeLayout 
  } = useWorkspace();

  // Initialize demo layouts
  useEffect(() => {
    if (layouts.length === 0) {
      // Create the default layouts
      Object.entries(defaultLayouts).forEach(([key, config]) => {
        createLayout(config);
      });
    }
  }, [layouts.length, createLayout]);

  const handleCreateCodeEditor = () => {
    createLayout(defaultLayouts.codeEditor);
  };

  const handleCreateDashboard = () => {
    createLayout(defaultLayouts.dashboard);
  };

  const handleCreateAIWorkspace = () => {
    createLayout(defaultLayouts.aiWorkspace);
  };

  const addSampleTab = (layoutId: string, groupId: string) => {
    const sampleTabs: TabConfig[] = [
      {
        id: `tab-${Date.now()}-1`,
        title: 'App.tsx',
        content: SampleCodeEditor,
        closable: true,
        icon: Code,
        isDirty: false,
        path: '/src/App.tsx',
      },
      {
        id: `tab-${Date.now()}-2`,
        title: 'README.md',
        content: SampleMarkdownEditor,
        closable: true,
        icon: FileText,
        isDirty: true,
        path: '/README.md',
      },
    ];

    const randomTab = sampleTabs[Math.floor(Math.random() * sampleTabs.length)];
    addTab(layoutId, groupId, randomTab);
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Workspace Demo</h1>
              <p className="text-muted-foreground">
                VSCode-inspired workspace layouts with resizable panels and intelligent tabs
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreateCodeEditor}
                className="flex items-center gap-2"
              >
                <Code className="h-4 w-4" />
                Code Editor
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreateDashboard}
                className="flex items-center gap-2"
              >
                <LayoutDashboard className="h-4 w-4" />
                Dashboard
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreateAIWorkspace}
                className="flex items-center gap-2"
              >
                <Bot className="h-4 w-4" />
                AI Workspace
              </Button>
              
              {activeLayout && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => addSampleTab(activeLayout.id, 'main-editor')}
                  className="flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Add Tab
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Workspace Area */}
      <div className="flex-1 overflow-hidden">
        {activeLayout ? (
          <WorkspaceLayout />
        ) : (
          <div className="h-full flex items-center justify-center">
            <Card className="w-96">
              <CardHeader className="text-center">
                <Monitor className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <CardTitle>Welcome to Workspace Demo</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-center text-muted-foreground">
                  Choose a workspace layout to get started
                </p>
                
                <div className="grid gap-2">
                  <Button
                    variant="outline"
                    onClick={handleCreateCodeEditor}
                    className="justify-start"
                  >
                    <Code className="h-4 w-4 mr-2" />
                    Code Editor Layout
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={handleCreateDashboard}
                    className="justify-start"
                  >
                    <LayoutDashboard className="h-4 w-4 mr-2" />
                    Dashboard Layout
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={handleCreateAIWorkspace}
                    className="justify-start"
                  >
                    <Bot className="h-4 w-4 mr-2" />
                    AI Workspace Layout
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}

// Sample tab content components
function SampleCodeEditor() {
  return (
    <div className="h-full p-4 font-mono text-sm">
      <div className="space-y-2">
        <div className="text-blue-500">import React from 'react';</div>
        <div className="text-blue-500">import { Button } from '@/components/ui/button';</div>
        <div></div>
        <div className="text-purple-500">function App() {`{`}</div>
        <div className="pl-4 text-green-500">return (</div>
        <div className="pl-8 text-gray-400">&lt;div className="app"&gt;</div>
        <div className="pl-12 text-gray-400">&lt;h1&gt;Hello World&lt;/h1&gt;</div>
        <div className="pl-12 text-gray-400">&lt;Button&gt;Click me&lt;/Button&gt;</div>
        <div className="pl-8 text-gray-400">&lt;/div&gt;</div>
        <div className="pl-4 text-green-500">);</div>
        <div className="text-purple-500">{`}`}</div>
        <div></div>
        <div className="text-blue-500">export default App;</div>
      </div>
    </div>
  );
}

function SampleMarkdownEditor() {
  return (
    <div className="h-full p-4">
      <div className="space-y-4">
        <h1 className="text-2xl font-bold"># Omnispace Workspace</h1>
        <p>This is a sample markdown file demonstrating the workspace layout system.</p>
        
        <h2 className="text-xl font-semibold">## Features</h2>
        <ul className="list-disc pl-6 space-y-1">
          <li>Resizable panels using react-resizable-panels</li>
          <li>Intelligent tab management with drag & drop</li>
          <li>VSCode-inspired interface design</li>
          <li>Modular panel system</li>
          <li>Persistent workspace state with Zustand</li>
        </ul>
        
        <h2 className="text-xl font-semibold">## Panel Types</h2>
        <ul className="list-disc pl-6 space-y-1">
          <li>Explorer - File tree navigation</li>
          <li>Terminal - Integrated terminal</li>
          <li>AI Chat - AI assistant integration</li>
          <li>Search - Global search functionality</li>
          <li>Git - Source control management</li>
          <li>Debug - Debugging tools</li>
          <li>Output - Build and runtime output</li>
          <li>Problems - Error and warning display</li>
        </ul>
      </div>
    </div>
  );
}

export default function WorkspaceDemoPage() {
  return <WorkspaceDemoContent />;
}
