import { Metadata } from 'next';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { UserProfile } from '@/components/auth/user-profile';

export const metadata: Metadata = {
  title: 'Profile - Omnispace',
  description: 'Manage your profile information and account settings.',
};

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-foreground">Profile Settings</h1>
              <p className="text-muted-foreground mt-2">
                Manage your personal information and account preferences.
              </p>
            </div>
            
            <UserProfile />
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
