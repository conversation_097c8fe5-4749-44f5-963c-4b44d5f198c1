import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Omnispace - AI-Powered Remote Workspace Platform",
  description: "Cloud-based development environments with Firecracker MicroVMs, VNC access, and AI-powered computer use. Scalable, secure, and intelligent remote workspaces.",
  keywords: ["remote workspace", "cloud development", "firecracker", "microvm", "vnc", "ai", "development environment"],
  authors: [{ name: "Omnispace Team" }],
  openGraph: {
    title: "Omnispace - AI-Powered Remote Workspace Platform",
    description: "Cloud-based development environments with Firecracker MicroVMs, VNC access, and AI-powered computer use.",
    type: "website",
    url: "https://omnispace.dev",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Omnispace Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Omnispace - AI-Powered Remote Workspace Platform",
    description: "Cloud-based development environments with Firecracker MicroVMs, VNC access, and AI-powered computer use.",
    images: ["/og-image.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
