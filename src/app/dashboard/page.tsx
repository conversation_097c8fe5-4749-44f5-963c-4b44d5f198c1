"use client";

import { AppSidebar } from "@/components/app-sidebar";
import { VMManager } from "@/components/VMManager";
import { VNCClient } from "@/components/VNCClient";
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { ProtectedRoute } from "@/components/auth/protected-route";
import React, { useState } from "react";

interface MicroVM {
  id: string;
  name: string;
  status: "running" | "stopped" | "starting" | "stopping";
  cpu: number;
  memory: number;
  diskSize: number;
  vncPort: number;
  createdAt: Date;
  lastAccessed?: Date;
}

export default function DashboardLayout() {
  const [selectedVM, setSelectedVM] = useState<MicroVM | null>(null);

  const handleVMSelect = (vm: MicroVM) => {
    setSelectedVM(vm);
  };

  const handleDisconnect = () => {
    setSelectedVM(null);
  };

  const getVNCUrl = (vm: MicroVM) => {
    return `ws://localhost:${vm.vncPort}`;
  };

  return (
    <ProtectedRoute>
      <SidebarProvider style={{ "--sidebar-width": "350px" } as React.CSSProperties}>
        <AppSidebar />
        <SidebarInset>
          {selectedVM ? (
            <div className="flex flex-1 flex-col gap-4 p-4">
              <header className="bg-background sticky top-0 flex shrink-0 items-center gap-2 border-b p-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
                <div className="flex items-center">
                  <h1 className="text-xl font-semibold">Connected to {selectedVM.name}</h1>
                </div>
              </header>
              <VNCClient url={getVNCUrl(selectedVM)} vmName={selectedVM.name} onDisconnect={handleDisconnect} />
            </div>
          ) : (
            <VMManager onSelectVM={handleVMSelect} />
          )}
        </SidebarInset>
      </SidebarProvider>
    </ProtectedRoute>
  );
}
