'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Bot, 
  Terminal, 
  FileText, 
  Monitor, 
  Container,
  Sparkles,
  Zap
} from 'lucide-react';

// Import our AI SDK UI components
import { AdvancedChat } from '@/components/ai/advanced-chat';
import { ObjectGeneration } from '@/components/ai/object-generation';
import { Completion } from '@/components/ai/completion';
import { StreamingData } from '@/components/ai/streaming-data';
import { GenerativeUI } from '@/components/ai/generative-ui';
import { useEnhancedChat } from '@/hooks/use-enhanced-chat';
import { createChatStorage } from '@/lib/chat-storage';

export default function WorkspaceAIPage() {
  const [activeWorkspace, setActiveWorkspace] = useState<string | null>(null);
  const [selectedVM, setSelectedVM] = useState<string | null>(null);

  // Initialize enhanced chat with workspace-specific configuration
  const chatStorage = createChatStorage();
  const workspaceChat = useEnhancedChat({
    id: `workspace-${activeWorkspace || 'default'}`,
    api: '/api/workspace-chat',
    persistence: {
      storage: chatStorage,
      autoSave: true,
      saveInterval: 30000,
    },
    maxMessages: 100,
    enableAnalytics: true,
    rateLimitConfig: {
      maxRequestsPerMinute: 20,
      maxRequestsPerHour: 200,
    },
  });

  // Mock workspace data (in real app, this would come from your VM management system)
  const mockWorkspaces = [
    {
      id: 'ws-dev-001',
      name: 'Development Environment',
      status: 'running',
      vmId: 'vm-firecracker-001',
      vncPort: 5901,
      type: 'ubuntu-dev',
    },
    {
      id: 'ws-test-002', 
      name: 'Testing Environment',
      status: 'stopped',
      vmId: 'vm-firecracker-002',
      vncPort: 5902,
      type: 'ubuntu-test',
    },
  ];

  const handleWorkspaceSelect = (workspaceId: string) => {
    setActiveWorkspace(workspaceId);
    const workspace = mockWorkspaces.find(w => w.id === workspaceId);
    if (workspace) {
      setSelectedVM(workspace.vmId);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
            <Bot className="h-10 w-10 text-blue-600" />
            Omnispace AI Assistant
          </h1>
          <p className="text-lg text-muted-foreground mb-6">
            AI-powered workspace management and computer use capabilities
          </p>
          
          {/* Workspace Status */}
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              <span className="text-sm">
                Active Workspace: {activeWorkspace || 'None'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Container className="h-5 w-5" />
              <span className="text-sm">
                VM: {selectedVM || 'None'}
              </span>
            </div>
          </div>

          {/* Workspace Selector */}
          <div className="flex justify-center gap-2 mb-8">
            {mockWorkspaces.map((workspace) => (
              <Button
                key={workspace.id}
                variant={activeWorkspace === workspace.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleWorkspaceSelect(workspace.id)}
                className="flex items-center gap-2"
              >
                <div className={`w-2 h-2 rounded-full ${
                  workspace.status === 'running' ? 'bg-green-500' : 'bg-gray-400'
                }`} />
                {workspace.name}
              </Button>
            ))}
          </div>
        </div>

        <Tabs defaultValue="chat" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <Bot className="h-4 w-4" />
              AI Chat
            </TabsTrigger>
            <TabsTrigger value="terminal" className="flex items-center gap-2">
              <Terminal className="h-4 w-4" />
              Terminal AI
            </TabsTrigger>
            <TabsTrigger value="files" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              File AI
            </TabsTrigger>
            <TabsTrigger value="automation" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Automation
            </TabsTrigger>
            <TabsTrigger value="streaming" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Live Data
            </TabsTrigger>
          </TabsList>

          {/* AI Chat Tab */}
          <TabsContent value="chat" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <AdvancedChat
                  id={`workspace-chat-${activeWorkspace}`}
                  api="/api/workspace-chat"
                  title="Workspace AI Assistant"
                  subtitle={`Connected to ${activeWorkspace || 'No workspace'}`}
                  placeholder="Ask me to help with your workspace, run commands, manage files, or control applications..."
                  showTimestamps={true}
                  showTokenCount={true}
                  allowFileUpload={true}
                  allowRegenerate={true}
                  allowDelete={true}
                  allowCopy={true}
                  allowExport={true}
                  onMessageSent={(message) => {
                    console.log('Message sent:', message);
                  }}
                  onMessageReceived={(message) => {
                    console.log('Message received:', message);
                  }}
                />
              </div>
              
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Chat Analytics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Messages:</span>
                      <Badge variant="outline">{workspaceChat.getMessageCount()}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Tokens:</span>
                      <Badge variant="outline">{workspaceChat.getTokenCount()}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Avg Response:</span>
                      <Badge variant="outline">
                        {workspaceChat.getAverageResponseTime().toFixed(0)}ms
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Most Used Tool:</span>
                      <Badge variant="outline">{workspaceChat.getMostUsedTool() || 'None'}</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full justify-start"
                      onClick={() => workspaceChat.sendMessage({ 
                        text: "Show me the current directory contents" 
                      })}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      List Files
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full justify-start"
                      onClick={() => workspaceChat.sendMessage({ 
                        text: "Check system resources and running processes" 
                      })}
                    >
                      <Monitor className="h-4 w-4 mr-2" />
                      System Status
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full justify-start"
                      onClick={() => workspaceChat.sendMessage({ 
                        text: "Open a terminal session" 
                      })}
                    >
                      <Terminal className="h-4 w-4 mr-2" />
                      Open Terminal
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Terminal AI Tab */}
          <TabsContent value="terminal" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>AI-Powered Terminal</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Natural language terminal commands with AI assistance
                </p>
              </CardHeader>
              <CardContent>
                <Completion
                  api="/api/terminal-completion"
                  placeholder="Describe what you want to do in the terminal (e.g., 'Find all Python files modified in the last week')"
                  showSettings={true}
                  showStats={true}
                  allowExport={true}
                  onComplete={(completion, prompt) => {
                    console.log('Terminal command generated:', { prompt, completion });
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* File AI Tab */}
          <TabsContent value="files" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <ObjectGeneration
                api="/api/generate-file"
                defaultType="article"
                showTypeSelector={true}
                showProgress={true}
                allowExport={true}
                onGenerated={(object, type) => {
                  console.log('File generated:', { type, object });
                }}
              />
              
              <Card>
                <CardHeader>
                  <CardTitle>File Operations</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    AI-powered file management and code generation
                  </div>
                  {/* File operation components would go here */}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Automation Tab */}
          <TabsContent value="automation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Workspace Automation</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Create and manage automated workflows for your workspace
                </p>
              </CardHeader>
              <CardContent>
                <GenerativeUI
                  toolCalls={[]}
                  dataParts={[]}
                  enablePreview={true}
                  showSource={false}
                  onToolUpdate={(toolId, data) => {
                    console.log('Tool updated:', { toolId, data });
                  }}
                  onError={(error) => {
                    console.error('Generative UI error:', error);
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Streaming Data Tab */}
          <TabsContent value="streaming" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <StreamingData
                endpoint="/api/workspace-stream"
                autoStart={false}
                maxRetries={3}
                retryDelay={1000}
                onData={(data) => {
                  console.log('Streaming data received:', data);
                }}
                onError={(error) => {
                  console.error('Streaming error:', error);
                }}
                onComplete={() => {
                  console.log('Streaming completed');
                }}
              />
              
              <Card>
                <CardHeader>
                  <CardTitle>Live Workspace Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      Real-time monitoring of workspace resources and activities
                    </div>
                    {/* Live metrics would be displayed here */}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
