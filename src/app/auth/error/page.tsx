'use client';

import { Suspense } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Cloud, AlertCircle, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case 'access_denied':
        return {
          title: 'Access Denied',
          description: 'You cancelled the authentication process or denied access to your account.',
          suggestion: 'Please try signing in again and grant the necessary permissions.',
        };
      case 'invalid_request':
        return {
          title: 'Invalid Request',
          description: 'The authentication request was invalid or malformed.',
          suggestion: 'Please try signing in again. If the problem persists, contact support.',
        };
      case 'unauthorized_client':
        return {
          title: 'Unauthorized Client',
          description: 'The application is not authorized to perform this authentication.',
          suggestion: 'This appears to be a configuration issue. Please contact support.',
        };
      case 'unsupported_response_type':
        return {
          title: 'Unsupported Response Type',
          description: 'The authentication provider does not support the requested response type.',
          suggestion: 'This appears to be a technical issue. Please contact support.',
        };
      case 'invalid_scope':
        return {
          title: 'Invalid Scope',
          description: 'The requested permissions are invalid or not supported.',
          suggestion: 'Please try signing in again. If the problem persists, contact support.',
        };
      case 'server_error':
        return {
          title: 'Server Error',
          description: 'The authentication server encountered an internal error.',
          suggestion: 'Please try again in a few moments. If the problem persists, contact support.',
        };
      case 'temporarily_unavailable':
        return {
          title: 'Service Temporarily Unavailable',
          description: 'The authentication service is temporarily unavailable.',
          suggestion: 'Please try again in a few moments.',
        };
      default:
        return {
          title: 'Authentication Error',
          description: errorDescription || 'An unexpected error occurred during authentication.',
          suggestion: 'Please try signing in again. If the problem persists, contact support.',
        };
    }
  };

  const errorInfo = getErrorMessage(error);

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
          <AlertCircle className="h-6 w-6 text-red-600" />
        </div>
        <CardTitle className="text-2xl font-bold">{errorInfo.title}</CardTitle>
        <CardDescription>{errorInfo.description}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{errorInfo.suggestion}</AlertDescription>
        </Alert>

        {error && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Technical Details:</h4>
            <div className="text-xs text-muted-foreground space-y-1">
              <div><strong>Error:</strong> {error}</div>
              {errorDescription && (
                <div><strong>Description:</strong> {errorDescription}</div>
              )}
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Link href="/auth/login" className="w-full block">
            <Button className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </Link>
          
          <Link href="/auth/register" className="w-full block">
            <Button variant="outline" className="w-full">
              Create New Account
            </Button>
          </Link>
          
          <Link href="/contact" className="w-full block">
            <Button variant="ghost" className="w-full">
              Contact Support
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

export default function AuthErrorPage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900 p-4">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-20 w-24 h-24 bg-green-500/10 rounded-full blur-xl animate-pulse delay-2000" />
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-yellow-500/10 rounded-full blur-xl animate-pulse delay-3000" />
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Logo */}
        <div className="flex items-center justify-center mb-8">
          <Link href="/" className="flex items-center gap-3">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-lg">
              <Cloud className="h-7 w-7 text-white" />
            </div>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              Omnispace
            </span>
          </Link>
        </div>

        {/* Error Content */}
        <Suspense fallback={
          <Card className="w-full max-w-md mx-auto">
            <CardContent className="p-6">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            </CardContent>
          </Card>
        }>
          <AuthErrorContent />
        </Suspense>

        {/* Additional Links */}
        <div className="mt-8 text-center space-y-4">
          <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground">
            <Link href="/help" className="hover:text-foreground transition-colors">
              Help
            </Link>
            <Link href="/privacy" className="hover:text-foreground transition-colors">
              Privacy
            </Link>
            <Link href="/terms" className="hover:text-foreground transition-colors">
              Terms
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
