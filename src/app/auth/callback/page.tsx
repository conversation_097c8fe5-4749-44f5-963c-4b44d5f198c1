'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Cloud, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { checkSession } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string | null>(null);

  const redirectTo = searchParams.get('redirect') || '/dashboard';

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Check if user is authenticated after OAuth callback
        const isAuthenticated = await checkSession();
        
        if (isAuthenticated) {
          setStatus('success');
          // Redirect after a short delay to show success message
          setTimeout(() => {
            router.push(redirectTo);
          }, 2000);
        } else {
          setStatus('error');
          setError('Authentication failed. Please try again.');
        }
      } catch (error: any) {
        setStatus('error');
        setError(error.message || 'An unexpected error occurred during authentication.');
      }
    };

    handleCallback();
  }, [checkSession, router, redirectTo]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900 p-4">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-20 w-24 h-24 bg-green-500/10 rounded-full blur-xl animate-pulse delay-2000" />
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-yellow-500/10 rounded-full blur-xl animate-pulse delay-3000" />
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Logo */}
        <div className="flex items-center justify-center mb-8">
          <Link href="/" className="flex items-center gap-3">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-lg">
              <Cloud className="h-7 w-7 text-white" />
            </div>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              Omnispace
            </span>
          </Link>
        </div>

        {/* Status Card */}
        <Card className="w-full">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">
              {status === 'loading' && (
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              )}
              {status === 'success' && (
                <div className="bg-green-100 rounded-full p-3">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              )}
              {status === 'error' && (
                <div className="bg-red-100 rounded-full p-3">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
              )}
            </div>

            <CardTitle className="text-2xl font-bold">
              {status === 'loading' && 'Completing sign in...'}
              {status === 'success' && 'Welcome to Omnispace!'}
              {status === 'error' && 'Authentication failed'}
            </CardTitle>

            <CardDescription>
              {status === 'loading' && 'Please wait while we complete your authentication.'}
              {status === 'success' && 'You have been successfully signed in. Redirecting you now...'}
              {status === 'error' && 'There was a problem signing you in.'}
            </CardDescription>
          </CardHeader>

          {(status === 'error' || status === 'success') && (
            <CardContent className="space-y-4">
              {status === 'error' && error && (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
                </div>
              )}

              {status === 'success' && (
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Authentication successful! You'll be redirected automatically, or you can click below to continue.
                  </p>
                </div>
              )}

              <div className="space-y-2">
                {status === 'success' && (
                  <Button 
                    className="w-full" 
                    onClick={() => router.push(redirectTo)}
                  >
                    Continue to Dashboard
                  </Button>
                )}

                {status === 'error' && (
                  <>
                    <Link href="/auth/login" className="w-full block">
                      <Button className="w-full">
                        Try again
                      </Button>
                    </Link>
                    
                    <Link href="/contact" className="w-full block">
                      <Button variant="outline" className="w-full">
                        Contact Support
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            </CardContent>
          )}
        </Card>

        {/* Additional Links */}
        <div className="mt-8 text-center space-y-4">
          <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground">
            <Link href="/help" className="hover:text-foreground transition-colors">
              Help
            </Link>
            <Link href="/privacy" className="hover:text-foreground transition-colors">
              Privacy
            </Link>
            <Link href="/terms" className="hover:text-foreground transition-colors">
              Terms
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
