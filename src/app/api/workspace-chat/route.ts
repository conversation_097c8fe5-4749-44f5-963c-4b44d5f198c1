import { openai } from '@ai-sdk/openai';
import { streamText, UIMessage, convertToModelMessages, tool, stepCountIs } from 'ai';
import { z } from 'zod';
import { createErrorResponse, withRetry } from '@/lib/ai-error-handler';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    const { messages }: { messages: UIMessage[] } = await req.json();

    // Validate that messages exist
    if (!messages || !Array.isArray(messages)) {
      return Response.json({ error: 'Invalid messages format', code: 'INVALID_INPUT' }, { status: 400 });
    }

    const result = await withRetry(async () => streamText({
      model: openai('gpt-4o-mini'),
      messages: convertToModelMessages(messages),
      temperature: 0.7,
      stopWhen: stepCountIs(5),
      tools: {
        // Workspace management tools
        terminal: tool({
          description: 'Execute terminal commands in the workspace Docker container',
          inputSchema: z.object({
            command: z.string().describe('The terminal command to execute'),
            workingDirectory: z.string().optional().describe('Working directory for the command (default: /workspace)'),
          }),
          execute: async ({ command, workingDirectory = '/workspace' }) => {
            // In a real implementation, this would:
            // 1. Connect to the Docker container via API
            // 2. Execute the command in the specified directory
            // 3. Return the actual output
            
            // Simulate different command responses
            if (command === 'ls' || command === 'ls -la') {
              return {
                command,
                workingDirectory,
                output: [
                  'total 24',
                  'drwxr-xr-x  4 <USER> <GROUP> 4096 Jan 29 10:30 .',
                  'drwxr-xr-x  3 <USER> <GROUP> 4096 Jan 29 10:00 ..',
                  '-rw-r--r--  1 <USER> <GROUP>  220 Jan 29 10:00 .bashrc',
                  'drwxr-xr-x  2 <USER> <GROUP> 4096 Jan 29 10:30 projects',
                  '-rw-r--r--  1 <USER> <GROUP> 1024 Jan 29 10:15 README.md',
                ],
                exitCode: 0,
              };
            }
            
            if (command === 'pwd') {
              return {
                command,
                workingDirectory,
                output: [workingDirectory],
                exitCode: 0,
              };
            }
            
            if (command.startsWith('docker ')) {
              return {
                command,
                workingDirectory,
                output: ['Docker command executed successfully'],
                exitCode: 0,
              };
            }
            
            return {
              command,
              workingDirectory,
              output: [`Executed: ${command}`, 'Command completed'],
              exitCode: 0,
            };
          },
        }),
        
        file_operations: tool({
          description: 'Perform file operations in the workspace',
          inputSchema: z.object({
            operation: z.enum(['read', 'write', 'create', 'delete', 'list']).describe('The file operation to perform'),
            path: z.string().describe('The file or directory path'),
            content: z.string().optional().describe('Content for write/create operations'),
          }),
          execute: async ({ operation, path, content }) => {
            // Simulate file operations
            // In real implementation, this would interact with the Docker container filesystem
            
            switch (operation) {
              case 'read':
                return {
                  operation,
                  path,
                  content: `Contents of ${path}:\n\nThis is simulated file content.`,
                  success: true,
                };
              case 'write':
              case 'create':
                return {
                  operation,
                  path,
                  content,
                  success: true,
                  message: `File ${operation === 'create' ? 'created' : 'updated'} successfully`,
                };
              case 'delete':
                return {
                  operation,
                  path,
                  success: true,
                  message: 'File deleted successfully',
                };
              case 'list':
                return {
                  operation,
                  path,
                  files: [
                    { name: 'README.md', type: 'file', size: 1024 },
                    { name: 'src', type: 'directory', size: 0 },
                    { name: 'package.json', type: 'file', size: 512 },
                  ],
                  success: true,
                };
              default:
                return {
                  operation,
                  path,
                  success: false,
                  error: 'Unknown operation',
                };
            }
          },
        }),
        
        vnc_control: tool({
          description: 'Control VNC desktop session in the workspace',
          inputSchema: z.object({
            action: z.enum(['screenshot', 'click', 'type', 'key', 'status']).describe('VNC action to perform'),
            x: z.number().optional().describe('X coordinate for click actions'),
            y: z.number().optional().describe('Y coordinate for click actions'),
            text: z.string().optional().describe('Text to type'),
            key: z.string().optional().describe('Key to press (e.g., "Enter", "Ctrl+C")'),
          }),
          execute: async ({ action, x, y, text, key }) => {
            // Simulate VNC operations
            // In real implementation, this would interact with the VNC server
            
            switch (action) {
              case 'screenshot':
                return {
                  action,
                  success: true,
                  screenshot_url: '/api/workspace/screenshot',
                  timestamp: Date.now(),
                };
              case 'click':
                return {
                  action,
                  x,
                  y,
                  success: true,
                  message: `Clicked at coordinates (${x}, ${y})`,
                };
              case 'type':
                return {
                  action,
                  text,
                  success: true,
                  message: `Typed: "${text}"`,
                };
              case 'key':
                return {
                  action,
                  key,
                  success: true,
                  message: `Pressed key: ${key}`,
                };
              case 'status':
                return {
                  action,
                  success: true,
                  status: 'connected',
                  resolution: '1920x1080',
                  connected_clients: 1,
                };
              default:
                return {
                  action,
                  success: false,
                  error: 'Unknown VNC action',
                };
            }
          },
        }),
        
        vm_management: tool({
          description: 'Manage Firecracker VM and Docker container',
          inputSchema: z.object({
            action: z.enum(['status', 'start', 'stop', 'restart', 'logs', 'resources']).describe('VM management action'),
            vmId: z.string().optional().describe('VM identifier'),
          }),
          execute: async ({ action, vmId = 'default' }) => {
            // Simulate VM management operations
            // In real implementation, this would interact with Firecracker and Docker APIs
            
            switch (action) {
              case 'status':
                return {
                  action,
                  vmId,
                  status: 'running',
                  uptime: '2h 15m',
                  cpu_usage: '15%',
                  memory_usage: '512MB / 2GB',
                  success: true,
                };
              case 'start':
                return {
                  action,
                  vmId,
                  success: true,
                  message: 'VM started successfully',
                  status: 'running',
                };
              case 'stop':
                return {
                  action,
                  vmId,
                  success: true,
                  message: 'VM stopped successfully',
                  status: 'stopped',
                };
              case 'restart':
                return {
                  action,
                  vmId,
                  success: true,
                  message: 'VM restarted successfully',
                  status: 'running',
                };
              case 'logs':
                return {
                  action,
                  vmId,
                  logs: [
                    '[2025-01-29 10:30:15] VM started',
                    '[2025-01-29 10:30:16] Docker container initialized',
                    '[2025-01-29 10:30:17] VNC server started on port 5901',
                    '[2025-01-29 10:30:18] Workspace ready',
                  ],
                  success: true,
                };
              case 'resources':
                return {
                  action,
                  vmId,
                  resources: {
                    cpu: { cores: 2, usage: '15%' },
                    memory: { total: '2GB', used: '512MB', available: '1.5GB' },
                    disk: { total: '20GB', used: '5GB', available: '15GB' },
                    network: { rx: '1.2MB', tx: '0.8MB' },
                  },
                  success: true,
                };
              default:
                return {
                  action,
                  vmId,
                  success: false,
                  error: 'Unknown VM action',
                };
            }
          },
        }),
      },
    }));

    return result.toUIMessageStreamResponse();
  } catch (error) {
    return createErrorResponse(error);
  }
}
