import { openai } from '@ai-sdk/openai';
import { generateObject } from 'ai';
import { z } from 'zod';
import { createErrorResponse, withRetry } from '@/lib/ai-error-handler';

export const maxDuration = 30;

// Schema for structured data generation
const PersonSchema = z.object({
  name: z.string().describe('The person\'s full name'),
  age: z.number().describe('The person\'s age in years'),
  occupation: z.string().describe('The person\'s job or profession'),
  location: z.object({
    city: z.string().describe('The city where the person lives'),
    country: z.string().describe('The country where the person lives'),
  }).describe('The person\'s location'),
  hobbies: z.array(z.string()).describe('List of the person\'s hobbies'),
  personality: z.object({
    traits: z.array(z.string()).describe('List of personality traits'),
    description: z.string().describe('Brief personality description'),
  }).describe('Personality information'),
});

const RecipeSchema = z.object({
  name: z.string().describe('The recipe name'),
  description: z.string().describe('Brief description of the dish'),
  servings: z.number().describe('Number of servings'),
  prepTime: z.string().describe('Preparation time (e.g., "30 minutes")'),
  cookTime: z.string().describe('Cooking time (e.g., "45 minutes")'),
  difficulty: z.enum(['easy', 'medium', 'hard']).describe('Difficulty level'),
  ingredients: z.array(z.object({
    item: z.string().describe('Ingredient name'),
    amount: z.string().describe('Amount needed (e.g., "2 cups", "1 tbsp")'),
  })).describe('List of ingredients'),
  instructions: z.array(z.string()).describe('Step-by-step cooking instructions'),
  tags: z.array(z.string()).describe('Recipe tags (e.g., "vegetarian", "quick", "healthy")'),
});

export async function POST(req: Request) {
  try {
    const { prompt, type } = await req.json();

    if (!prompt || !type) {
      return Response.json({ error: 'Prompt and type are required' }, { status: 400 });
    }

    let schema;
    let systemPrompt;

    switch (type) {
      case 'person':
        schema = PersonSchema;
        systemPrompt = 'Generate a realistic fictional person based on the user prompt. Be creative but realistic.';
        break;
      case 'recipe':
        schema = RecipeSchema;
        systemPrompt = 'Generate a detailed recipe based on the user prompt. Include realistic ingredients and instructions.';
        break;
      default:
        return Response.json({ error: 'Invalid type. Use "person" or "recipe"' }, { status: 400 });
    }

    const result = await withRetry(async () => generateObject({
      model: openai('gpt-4o-mini'),
      schema,
      prompt: `${systemPrompt}\n\nUser request: ${prompt}`,
      temperature: 0.7,
    }));

    return Response.json({
      success: true,
      data: result.object,
      usage: result.usage,
    });

  } catch (error) {
    return createErrorResponse(error);
  }
}
