import { openai } from '@ai-sdk/openai';
import { embed, embedMany } from 'ai';
import { createErrorResponse, withRetry } from '@/lib/ai-error-handler';

export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    const { text, texts } = await req.json();

    if (!text && !texts) {
      return Response.json({ error: 'Either "text" or "texts" array is required' }, { status: 400 });
    }

    if (text && texts) {
      return Response.json({ error: 'Provide either "text" or "texts", not both' }, { status: 400 });
    }

    const model = openai.embedding('text-embedding-3-small');

    if (text) {
      // Single text embedding
      const result = await withRetry(async () => embed({
        model,
        value: text,
      }));

      return Response.json({
        success: true,
        embedding: result.embedding,
        usage: result.usage,
        dimensions: result.embedding.length,
      });
    } else {
      // Multiple texts embedding
      if (!Array.isArray(texts) || texts.length === 0) {
        return Response.json({ error: 'texts must be a non-empty array', code: 'INVALID_INPUT' }, { status: 400 });
      }

      if (texts.length > 100) {
        return Response.json({ error: 'Maximum 100 texts allowed per request', code: 'TOO_MANY_VALUES' }, { status: 400 });
      }

      const result = await withRetry(async () => embedMany({
        model,
        values: texts,
      }));

      return Response.json({
        success: true,
        embeddings: result.embeddings,
        usage: result.usage,
        count: result.embeddings.length,
        dimensions: result.embeddings[0]?.length || 0,
      });
    }

  } catch (error) {
    return createErrorResponse(error);
  }
}
