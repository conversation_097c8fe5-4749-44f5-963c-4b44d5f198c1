import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';

// POST /api/containers/[id]/restart - Restart container
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dockerService.restartContainer(params.id);
    
    return NextResponse.json({
      success: true,
      message: 'Container restarted successfully',
    });
  } catch (error) {
    console.error(`Error restarting container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to restart container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
