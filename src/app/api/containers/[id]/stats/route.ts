import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';

// GET /api/containers/[id]/stats - Get container stats
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const stats = await dockerService.getContainerStats(params.id);
    
    return NextResponse.json({
      success: true,
      data: {
        containerId: params.id,
        stats,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error(`Error getting stats for container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get container stats',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
