import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';

// POST /api/containers/[id]/exec - Execute command in container
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    if (!body.cmd || !Array.isArray(body.cmd) || body.cmd.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid command',
          message: 'Command must be provided as an array of strings',
        },
        { status: 400 }
      );
    }
    
    const output = await dockerService.execInContainer(params.id, body.cmd);
    
    return NextResponse.json({
      success: true,
      data: {
        containerId: params.id,
        command: body.cmd,
        output,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error(`Error executing command in container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to execute command',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
