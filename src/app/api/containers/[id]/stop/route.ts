import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';

// POST /api/containers/[id]/stop - Stop container
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const timeout = parseInt(searchParams.get('timeout') || '10');
    
    await dockerService.stopContainer(params.id, timeout);
    
    return NextResponse.json({
      success: true,
      message: 'Container stopped successfully',
    });
  } catch (error) {
    console.error(`Error stopping container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to stop container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
