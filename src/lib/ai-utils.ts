import { CustomUIMessage, MessagePart, TextPart, ToolPart, DataPart } from '@/types/ai-sdk-ui';

// ============================================================================
// Message Utilities
// ============================================================================

/**
 * Extract text content from a message
 */
export function extractTextFromMessage(message: CustomUIMessage): string {
  return message.parts
    .filter((part): part is TextPart => part.type === 'text')
    .map(part => part.text)
    .join(' ');
}

/**
 * Extract tool calls from a message
 */
export function extractToolCallsFromMessage(message: CustomUIMessage): ToolPart[] {
  return message.parts.filter((part): part is ToolPart => 
    part.type.startsWith('tool-')
  ) as ToolPart[];
}

/**
 * Extract data parts from a message
 */
export function extractDataPartsFromMessage(message: CustomUIMessage): DataPart[] {
  return message.parts.filter((part): part is DataPart => 
    part.type.startsWith('data-')
  ) as DataPart[];
}

/**
 * Count tokens in a message (approximate)
 */
export function estimateTokenCount(text: string): number {
  // Simple approximation: ~4 characters per token
  return Math.ceil(text.length / 4);
}

/**
 * Get message summary
 */
export function getMessageSummary(message: CustomUIMessage, maxLength: number = 100): string {
  const text = extractTextFromMessage(message);
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Check if message contains tools
 */
export function messageHasTools(message: CustomUIMessage): boolean {
  return message.parts.some(part => part.type.startsWith('tool-'));
}

/**
 * Check if message contains data parts
 */
export function messageHasData(message: CustomUIMessage): boolean {
  return message.parts.some(part => part.type.startsWith('data-'));
}

/**
 * Filter messages by role
 */
export function filterMessagesByRole(
  messages: CustomUIMessage[], 
  role: 'user' | 'assistant' | 'system'
): CustomUIMessage[] {
  return messages.filter(message => message.role === role);
}

/**
 * Get conversation statistics
 */
export function getConversationStats(messages: CustomUIMessage[]) {
  const userMessages = filterMessagesByRole(messages, 'user');
  const assistantMessages = filterMessagesByRole(messages, 'assistant');
  const systemMessages = filterMessagesByRole(messages, 'system');
  
  const totalTokens = messages.reduce((acc, message) => 
    acc + (message.metadata?.totalTokens || 0), 0
  );
  
  const toolCalls = messages.reduce((acc, message) => 
    acc + extractToolCallsFromMessage(message).length, 0
  );
  
  const totalCharacters = messages.reduce((acc, message) => 
    acc + extractTextFromMessage(message).length, 0
  );

  return {
    totalMessages: messages.length,
    userMessages: userMessages.length,
    assistantMessages: assistantMessages.length,
    systemMessages: systemMessages.length,
    totalTokens,
    totalCharacters,
    toolCalls,
    averageMessageLength: totalCharacters / messages.length || 0,
  };
}

// ============================================================================
// Streaming Utilities
// ============================================================================

/**
 * Parse streaming response chunk
 */
export function parseStreamChunk(chunk: string): any[] {
  const lines = chunk.split('\n').filter(line => line.trim());
  const events = [];
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      try {
        const data = JSON.parse(line.substring(6));
        events.push(data);
      } catch (error) {
        console.warn('Failed to parse stream chunk:', line);
      }
    }
  }
  
  return events;
}

/**
 * Create streaming response handler
 */
export function createStreamHandler(
  onData: (data: any) => void,
  onError: (error: Error) => void,
  onComplete: () => void
) {
  return async (response: Response) => {
    if (!response.body) {
      onError(new Error('No response body'));
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          onComplete();
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        const events = parseStreamChunk(chunk);
        
        events.forEach(event => onData(event));
      }
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Streaming error'));
    } finally {
      reader.releaseLock();
    }
  };
}

// ============================================================================
// Transport Utilities
// ============================================================================

/**
 * Create fetch request with retry logic
 */
export async function fetchWithRetry(
  url: string,
  options: RequestInit,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<Response> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, options);
      
      if (response.ok) {
        return response;
      }
      
      // Don't retry client errors (4xx)
      if (response.status >= 400 && response.status < 500) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Fetch failed');
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * Create request headers with authentication
 */
export function createRequestHeaders(
  customHeaders: Record<string, string> = {},
  includeAuth: boolean = true
): Record<string, string> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...customHeaders,
  };

  if (includeAuth) {
    // Add authentication headers if available
    const apiKey = process.env.NEXT_PUBLIC_API_KEY;
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }
  }

  return headers;
}

// ============================================================================
// Validation Utilities
// ============================================================================

/**
 * Validate message structure
 */
export function validateMessage(message: any): message is CustomUIMessage {
  if (!message || typeof message !== 'object') return false;
  if (!message.id || typeof message.id !== 'string') return false;
  if (!message.role || !['user', 'assistant', 'system'].includes(message.role)) return false;
  if (!Array.isArray(message.parts)) return false;
  
  return message.parts.every((part: any) => 
    part && typeof part === 'object' && typeof part.type === 'string'
  );
}

/**
 * Sanitize message content
 */
export function sanitizeMessage(message: CustomUIMessage): CustomUIMessage {
  return {
    ...message,
    parts: message.parts.map(part => {
      if (part.type === 'text') {
        return {
          ...part,
          text: (part as any).text?.toString() || '',
        };
      }
      return part;
    }),
  };
}

// ============================================================================
// Format Utilities
// ============================================================================

/**
 * Format timestamp for display
 */
export function formatTimestamp(timestamp: number, format: 'relative' | 'absolute' = 'relative'): string {
  const date = new Date(timestamp);
  const now = new Date();
  
  if (format === 'absolute') {
    return date.toLocaleString();
  }
  
  const diffMs = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMinutes < 1) return 'Just now';
  if (diffMinutes < 60) return `${diffMinutes}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString();
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
}

/**
 * Format duration
 */
export function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  }
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }
  return `${seconds}s`;
}

// ============================================================================
// Workspace-specific Utilities for Omnispace
// ============================================================================

/**
 * Parse workspace command output
 */
export function parseWorkspaceCommand(command: string, output: string) {
  const lines = output.split('\n').filter(line => line.trim());
  
  // Parse different command types
  if (command === 'ls' || command.startsWith('ls ')) {
    return {
      type: 'file_list',
      files: lines.slice(1).map(line => {
        const parts = line.split(/\s+/);
        return {
          permissions: parts[0],
          name: parts[parts.length - 1],
          size: parts[4],
          modified: parts.slice(5, 8).join(' '),
        };
      }),
    };
  }
  
  if (command === 'pwd') {
    return {
      type: 'current_directory',
      path: lines[0],
    };
  }
  
  if (command.startsWith('docker ')) {
    return {
      type: 'docker_command',
      command: command.substring(7),
      output: lines,
    };
  }
  
  return {
    type: 'generic',
    output: lines,
  };
}

/**
 * Generate workspace session ID
 */
export function generateWorkspaceSessionId(): string {
  return `ws-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate workspace path
 */
export function validateWorkspacePath(path: string): boolean {
  // Basic validation for workspace paths
  if (!path || typeof path !== 'string') return false;
  if (path.includes('..')) return false; // Prevent directory traversal
  if (!path.startsWith('/workspace') && !path.startsWith('/tmp')) return false;
  return true;
}

/**
 * Extract VNC connection info
 */
export function parseVNCConnection(connectionString: string) {
  const match = connectionString.match(/^vnc:\/\/([^:]+):(\d+)(?:\/(.+))?$/);
  if (!match) return null;
  
  return {
    host: match[1],
    port: parseInt(match[2]),
    path: match[3] || '',
  };
}
