/**
 * AI SDK V5 Functionality Tests
 * 
 * These tests verify that the AI SDK integration is working correctly.
 * Note: These are integration tests that require valid API keys.
 */

import { describe, it, expect, beforeAll } from '@jest/globals';

// Mock environment variables for testing
beforeAll(() => {
  process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || 'test-key';
});

describe('AI SDK V5 Integration', () => {
  describe('Chat API', () => {
    it('should handle valid chat requests', async () => {
      const response = await fetch('http://localhost:3000/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [
            { id: '1', role: 'user', parts: [{ type: 'text', text: 'Hello' }] }
          ]
        }),
      });

      // Should return a streaming response
      expect(response.ok).toBe(true);
      expect(response.headers.get('content-type')).toContain('text/plain');
    });

    it('should reject invalid message format', async () => {
      const response = await fetch('http://localhost:3000/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: 'invalid' }),
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('Invalid messages format');
    });
  });

  describe('Structured Data Generation API', () => {
    it('should generate person object', async () => {
      const response = await fetch('http://localhost:3000/api/generate-object', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: 'A software engineer from Tokyo',
          type: 'person'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(data.data).toHaveProperty('name');
        expect(data.data).toHaveProperty('age');
        expect(data.data).toHaveProperty('occupation');
        expect(data.data).toHaveProperty('location');
      }
    });

    it('should generate recipe object', async () => {
      const response = await fetch('http://localhost:3000/api/generate-object', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: 'A healthy vegetarian pasta dish',
          type: 'recipe'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(data.data).toHaveProperty('name');
        expect(data.data).toHaveProperty('ingredients');
        expect(data.data).toHaveProperty('instructions');
        expect(Array.isArray(data.data.ingredients)).toBe(true);
        expect(Array.isArray(data.data.instructions)).toBe(true);
      }
    });

    it('should reject invalid type', async () => {
      const response = await fetch('http://localhost:3000/api/generate-object', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: 'Test prompt',
          type: 'invalid'
        }),
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toContain('Invalid type');
    });
  });

  describe('Embeddings API', () => {
    it('should generate embeddings for single text', async () => {
      const response = await fetch('http://localhost:3000/api/embeddings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'This is a test sentence for embedding generation.'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(Array.isArray(data.embedding)).toBe(true);
        expect(data.dimensions).toBeGreaterThan(0);
        expect(data.embedding.length).toBe(data.dimensions);
      }
    });

    it('should generate embeddings for multiple texts', async () => {
      const response = await fetch('http://localhost:3000/api/embeddings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          texts: [
            'First test sentence.',
            'Second test sentence.',
            'Third test sentence.'
          ]
        }),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(Array.isArray(data.embeddings)).toBe(true);
        expect(data.count).toBe(3);
        expect(data.embeddings.length).toBe(3);
      }
    });

    it('should reject requests with both text and texts', async () => {
      const response = await fetch('http://localhost:3000/api/embeddings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'Single text',
          texts: ['Multiple', 'texts']
        }),
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toContain('Provide either "text" or "texts"');
    });
  });
});

// Utility function to test tool functionality
export async function testToolFunctionality() {
  console.log('Testing AI SDK V5 tool functionality...');
  
  const testCases = [
    'What is the weather in New York?',
    'Calculate 15 * 23',
    'What is 100 degrees Fahrenheit in Celsius?',
    'What is the weather in Tokyo in Celsius?'
  ];

  for (const testCase of testCases) {
    console.log(`\nTesting: "${testCase}"`);
    
    try {
      const response = await fetch('http://localhost:3000/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [
            { id: '1', role: 'user', parts: [{ type: 'text', text: testCase }] }
          ]
        }),
      });

      if (response.ok) {
        console.log('✅ Request successful');
        // Note: In a real test, you'd parse the streaming response
      } else {
        console.log('❌ Request failed:', response.status);
      }
    } catch (error) {
      console.log('❌ Error:', error);
    }
  }
}
