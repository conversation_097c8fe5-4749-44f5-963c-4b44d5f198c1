/**
 * AI SDK UI Components Test Suite
 * 
 * Comprehensive tests for AI SDK UI components and utilities
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Import utilities for testing
import {
  extractTextFromMessage,
  extractToolCallsFromMessage,
  getConversationStats,
  formatTimestamp,
  validateMessage,
  parseWorkspaceCommand,
  generateWorkspaceSessionId,
} from '../ai-utils';

import { IndexedDBChatStorage, LocalStorageChatStorage } from '../chat-storage';
import { CustomUIMessage } from '../../types/ai-sdk-ui';

// Mock data
const mockMessage: CustomUIMessage = {
  id: 'msg-1',
  role: 'user',
  parts: [
    { type: 'text', text: 'Hello, how are you?' },
    { type: 'tool-weather', args: { location: 'New York' }, result: { temperature: 72 } }
  ],
  metadata: {
    createdAt: Date.now(),
    totalTokens: 15,
    model: 'gpt-4o-mini',
  },
};

const mockMessages: CustomUIMessage[] = [
  mockMessage,
  {
    id: 'msg-2',
    role: 'assistant',
    parts: [
      { type: 'text', text: 'I am doing well, thank you!' }
    ],
    metadata: {
      createdAt: Date.now() + 1000,
      totalTokens: 12,
      model: 'gpt-4o-mini',
    },
  },
];

describe('AI Utilities', () => {
  describe('extractTextFromMessage', () => {
    it('should extract text from message parts', () => {
      const text = extractTextFromMessage(mockMessage);
      expect(text).toBe('Hello, how are you?');
    });

    it('should handle messages with no text parts', () => {
      const messageWithoutText: CustomUIMessage = {
        id: 'msg-3',
        role: 'assistant',
        parts: [
          { type: 'tool-calculator', args: { expression: '2+2' }, result: { result: 4 } }
        ],
      };
      const text = extractTextFromMessage(messageWithoutText);
      expect(text).toBe('');
    });
  });

  describe('extractToolCallsFromMessage', () => {
    it('should extract tool calls from message', () => {
      const toolCalls = extractToolCallsFromMessage(mockMessage);
      expect(toolCalls).toHaveLength(1);
      expect(toolCalls[0].type).toBe('tool-weather');
    });

    it('should return empty array for messages without tools', () => {
      const messageWithoutTools: CustomUIMessage = {
        id: 'msg-4',
        role: 'user',
        parts: [{ type: 'text', text: 'Just text' }],
      };
      const toolCalls = extractToolCallsFromMessage(messageWithoutTools);
      expect(toolCalls).toHaveLength(0);
    });
  });

  describe('getConversationStats', () => {
    it('should calculate conversation statistics', () => {
      const stats = getConversationStats(mockMessages);
      expect(stats.totalMessages).toBe(2);
      expect(stats.userMessages).toBe(1);
      expect(stats.assistantMessages).toBe(1);
      expect(stats.totalTokens).toBe(27);
      expect(stats.toolCalls).toBe(1);
    });

    it('should handle empty conversation', () => {
      const stats = getConversationStats([]);
      expect(stats.totalMessages).toBe(0);
      expect(stats.totalTokens).toBe(0);
      expect(stats.averageMessageLength).toBe(0);
    });
  });

  describe('formatTimestamp', () => {
    it('should format relative timestamps', () => {
      const now = Date.now();
      const fiveMinutesAgo = now - (5 * 60 * 1000);
      
      const formatted = formatTimestamp(fiveMinutesAgo, 'relative');
      expect(formatted).toBe('5m ago');
    });

    it('should format absolute timestamps', () => {
      const timestamp = new Date('2025-01-29T10:30:00Z').getTime();
      const formatted = formatTimestamp(timestamp, 'absolute');
      expect(formatted).toMatch(/2025/);
    });
  });

  describe('validateMessage', () => {
    it('should validate correct message structure', () => {
      expect(validateMessage(mockMessage)).toBe(true);
    });

    it('should reject invalid message structure', () => {
      expect(validateMessage(null)).toBe(false);
      expect(validateMessage({})).toBe(false);
      expect(validateMessage({ id: 'test' })).toBe(false);
      expect(validateMessage({ id: 'test', role: 'invalid' })).toBe(false);
    });
  });

  describe('parseWorkspaceCommand', () => {
    it('should parse ls command output', () => {
      const output = `total 24
drwxr-xr-x  4 <USER> <GROUP> 4096 Jan 29 10:30 .
-rw-r--r--  1 <USER> <GROUP>  220 Jan 29 10:00 .bashrc`;
      
      const parsed = parseWorkspaceCommand('ls -la', output);
      expect(parsed.type).toBe('file_list');
      expect(parsed.files).toHaveLength(2);
      expect(parsed.files[0].name).toBe('.');
    });

    it('should parse pwd command output', () => {
      const parsed = parseWorkspaceCommand('pwd', '/workspace/project');
      expect(parsed.type).toBe('current_directory');
      expect(parsed.path).toBe('/workspace/project');
    });

    it('should handle generic commands', () => {
      const parsed = parseWorkspaceCommand('echo hello', 'hello');
      expect(parsed.type).toBe('generic');
      expect(parsed.output).toEqual(['hello']);
    });
  });

  describe('generateWorkspaceSessionId', () => {
    it('should generate unique session IDs', () => {
      const id1 = generateWorkspaceSessionId();
      const id2 = generateWorkspaceSessionId();
      
      expect(id1).toMatch(/^ws-\d+-[a-z0-9]+$/);
      expect(id2).toMatch(/^ws-\d+-[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });
  });
});

describe('Chat Storage', () => {
  let storage: LocalStorageChatStorage;

  beforeEach(() => {
    storage = new LocalStorageChatStorage();
    // Clear localStorage before each test
    localStorage.clear();
  });

  afterEach(() => {
    localStorage.clear();
  });

  describe('LocalStorageChatStorage', () => {
    it('should save and load chat messages', async () => {
      const chatId = 'test-chat';
      await storage.saveChat(chatId, mockMessages);
      
      const loadedMessages = await storage.loadChat(chatId);
      expect(loadedMessages).toHaveLength(2);
      expect(loadedMessages[0].id).toBe('msg-1');
    });

    it('should list saved chats', async () => {
      await storage.saveChat('chat-1', mockMessages);
      await storage.saveChat('chat-2', mockMessages);
      
      const chats = await storage.listChats();
      expect(chats).toHaveLength(2);
      expect(chats.map(c => c.id)).toContain('chat-1');
      expect(chats.map(c => c.id)).toContain('chat-2');
    });

    it('should delete chats', async () => {
      const chatId = 'test-chat';
      await storage.saveChat(chatId, mockMessages);
      await storage.deleteChat(chatId);
      
      const chats = await storage.listChats();
      expect(chats.map(c => c.id)).not.toContain(chatId);
    });

    it('should create new chat', async () => {
      const chatId = await storage.createChat();
      expect(chatId).toMatch(/^chat-\d+-[a-z0-9]+$/);
      
      const chats = await storage.listChats();
      expect(chats.map(c => c.id)).toContain(chatId);
    });

    it('should handle non-existent chat', async () => {
      await expect(storage.loadChat('non-existent')).rejects.toThrow();
    });
  });
});

describe('Component Integration Tests', () => {
  // Mock fetch for API calls
  beforeEach(() => {
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('API Integration', () => {
    it('should handle successful chat API response', async () => {
      const mockResponse = {
        ok: true,
        body: {
          getReader: () => ({
            read: jest.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode('data: {"type":"text","text":"Hello"}\n\n')
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              }),
            releaseLock: jest.fn(),
          }),
        },
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: mockMessages }),
      });

      expect(response.ok).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith('/api/chat', expect.objectContaining({
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      }));
    });

    it('should handle API errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      await expect(
        fetch('/api/chat', {
          method: 'POST',
          body: JSON.stringify({ messages: mockMessages }),
        })
      ).rejects.toThrow('Network error');
    });
  });

  describe('Workspace Tool Integration', () => {
    it('should simulate terminal command execution', async () => {
      const mockTerminalResponse = {
        command: 'ls -la',
        workingDirectory: '/workspace',
        output: ['total 24', 'drwxr-xr-x  4 <USER> <GROUP> 4096 Jan 29 10:30 .'],
        exitCode: 0,
      };

      // This would be tested with actual component rendering
      expect(mockTerminalResponse.exitCode).toBe(0);
      expect(mockTerminalResponse.output).toHaveLength(2);
    });

    it('should handle file operations', async () => {
      const mockFileResponse = {
        operation: 'read',
        path: '/workspace/test.txt',
        content: 'File content',
        success: true,
      };

      expect(mockFileResponse.success).toBe(true);
      expect(mockFileResponse.content).toBe('File content');
    });
  });
});

describe('Error Handling', () => {
  it('should handle streaming errors', () => {
    const mockError = new Error('Streaming failed');
    
    // Test error handling logic
    expect(mockError.message).toBe('Streaming failed');
    expect(mockError instanceof Error).toBe(true);
  });

  it('should handle rate limiting', () => {
    const rateLimitError = new Error('Rate limit exceeded');
    
    // Test rate limit handling
    expect(rateLimitError.message).toContain('Rate limit');
  });
});

describe('Performance Tests', () => {
  it('should handle large message arrays efficiently', () => {
    const largeMessageArray = Array.from({ length: 1000 }, (_, i) => ({
      ...mockMessage,
      id: `msg-${i}`,
    }));

    const start = performance.now();
    const stats = getConversationStats(largeMessageArray);
    const end = performance.now();

    expect(stats.totalMessages).toBe(1000);
    expect(end - start).toBeLessThan(100); // Should complete in under 100ms
  });

  it('should efficiently extract text from messages', () => {
    const messagesWithLongText = Array.from({ length: 100 }, (_, i) => ({
      ...mockMessage,
      id: `msg-${i}`,
      parts: [{ type: 'text', text: 'A'.repeat(1000) }],
    }));

    const start = performance.now();
    messagesWithLongText.forEach(msg => extractTextFromMessage(msg));
    const end = performance.now();

    expect(end - start).toBeLessThan(50); // Should complete in under 50ms
  });
});

// Export test utilities for use in other test files
export {
  mockMessage,
  mockMessages,
};
