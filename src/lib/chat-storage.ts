import { CustomUIMessage, ChatStorage } from '@/types/ai-sdk-ui';

// IndexedDB-based storage implementation
export class IndexedDBChatStorage implements ChatStorage {
  private dbName = 'omnispace-chat-storage';
  private version = 1;
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create chats store
        if (!db.objectStoreNames.contains('chats')) {
          const chatStore = db.createObjectStore('chats', { keyPath: 'id' });
          chatStore.createIndex('updatedAt', 'updatedAt', { unique: false });
          chatStore.createIndex('title', 'title', { unique: false });
        }

        // Create messages store
        if (!db.objectStoreNames.contains('messages')) {
          const messageStore = db.createObjectStore('messages', { keyPath: 'id' });
          messageStore.createIndex('chatId', 'chatId', { unique: false });
          messageStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // Create chat metadata store
        if (!db.objectStoreNames.contains('chatMetadata')) {
          db.createObjectStore('chatMetadata', { keyPath: 'chatId' });
        }
      };
    });
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    if (!this.db) {
      throw new Error('Failed to initialize database');
    }
    return this.db;
  }

  async saveChat(chatId: string, messages: CustomUIMessage[]): Promise<void> {
    const db = await this.ensureDB();
    const transaction = db.transaction(['chats', 'messages', 'chatMetadata'], 'readwrite');

    try {
      const chatStore = transaction.objectStore('chats');
      const messageStore = transaction.objectStore('messages');
      const metadataStore = transaction.objectStore('chatMetadata');

      // Generate title from first user message
      const firstUserMessage = messages.find(m => m.role === 'user');
      const title = firstUserMessage?.parts
        .filter(p => p.type === 'text')
        .map(p => (p as any).text)
        .join(' ')
        .substring(0, 50) || 'New Chat';

      const chatData = {
        id: chatId,
        title: title + (title.length >= 50 ? '...' : ''),
        messageCount: messages.length,
        updatedAt: Date.now(),
        createdAt: Date.now(),
      };

      // Save or update chat
      await new Promise<void>((resolve, reject) => {
        const request = chatStore.put(chatData);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      // Clear existing messages for this chat
      await new Promise<void>((resolve, reject) => {
        const index = messageStore.index('chatId');
        const request = index.openCursor(IDBKeyRange.only(chatId));
        
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          } else {
            resolve();
          }
        };
        request.onerror = () => reject(request.error);
      });

      // Save new messages
      for (const message of messages) {
        const messageData = {
          ...message,
          chatId,
          timestamp: message.metadata?.createdAt || Date.now(),
        };

        await new Promise<void>((resolve, reject) => {
          const request = messageStore.put(messageData);
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      }

      // Save metadata
      const metadata = {
        chatId,
        totalTokens: messages.reduce((acc, m) => acc + (m.metadata?.totalTokens || 0), 0),
        lastModel: messages.findLast(m => m.metadata?.model)?.metadata?.model,
        tags: this.extractTags(messages),
        summary: this.generateSummary(messages),
      };

      await new Promise<void>((resolve, reject) => {
        const request = metadataStore.put(metadata);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      await new Promise<void>((resolve, reject) => {
        transaction.oncomplete = () => resolve();
        transaction.onerror = () => reject(transaction.error);
      });

    } catch (error) {
      transaction.abort();
      throw error;
    }
  }

  async loadChat(chatId: string): Promise<CustomUIMessage[]> {
    const db = await this.ensureDB();
    const transaction = db.transaction(['messages'], 'readonly');
    const messageStore = transaction.objectStore('messages');
    const index = messageStore.index('chatId');

    return new Promise((resolve, reject) => {
      const messages: CustomUIMessage[] = [];
      const request = index.openCursor(IDBKeyRange.only(chatId));

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          const messageData = cursor.value;
          // Remove storage-specific fields
          const { chatId: _, timestamp: __, ...message } = messageData;
          messages.push(message as CustomUIMessage);
          cursor.continue();
        } else {
          // Sort messages by creation time
          messages.sort((a, b) => {
            const aTime = a.metadata?.createdAt || 0;
            const bTime = b.metadata?.createdAt || 0;
            return aTime - bTime;
          });
          resolve(messages);
        }
      };

      request.onerror = () => reject(request.error);
    });
  }

  async deleteChat(chatId: string): Promise<void> {
    const db = await this.ensureDB();
    const transaction = db.transaction(['chats', 'messages', 'chatMetadata'], 'readwrite');

    try {
      const chatStore = transaction.objectStore('chats');
      const messageStore = transaction.objectStore('messages');
      const metadataStore = transaction.objectStore('chatMetadata');

      // Delete chat
      await new Promise<void>((resolve, reject) => {
        const request = chatStore.delete(chatId);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      // Delete messages
      await new Promise<void>((resolve, reject) => {
        const index = messageStore.index('chatId');
        const request = index.openCursor(IDBKeyRange.only(chatId));
        
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          } else {
            resolve();
          }
        };
        request.onerror = () => reject(request.error);
      });

      // Delete metadata
      await new Promise<void>((resolve, reject) => {
        const request = metadataStore.delete(chatId);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      await new Promise<void>((resolve, reject) => {
        transaction.oncomplete = () => resolve();
        transaction.onerror = () => reject(transaction.error);
      });

    } catch (error) {
      transaction.abort();
      throw error;
    }
  }

  async listChats(): Promise<{ id: string; title: string; updatedAt: number }[]> {
    const db = await this.ensureDB();
    const transaction = db.transaction(['chats'], 'readonly');
    const chatStore = transaction.objectStore('chats');
    const index = chatStore.index('updatedAt');

    return new Promise((resolve, reject) => {
      const chats: { id: string; title: string; updatedAt: number }[] = [];
      const request = index.openCursor(null, 'prev'); // Most recent first

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          const chat = cursor.value;
          chats.push({
            id: chat.id,
            title: chat.title,
            updatedAt: chat.updatedAt,
          });
          cursor.continue();
        } else {
          resolve(chats);
        }
      };

      request.onerror = () => reject(request.error);
    });
  }

  async createChat(): Promise<string> {
    const chatId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const db = await this.ensureDB();
    const transaction = db.transaction(['chats'], 'readwrite');
    const chatStore = transaction.objectStore('chats');

    const chatData = {
      id: chatId,
      title: 'New Chat',
      messageCount: 0,
      updatedAt: Date.now(),
      createdAt: Date.now(),
    };

    return new Promise((resolve, reject) => {
      const request = chatStore.put(chatData);
      request.onsuccess = () => resolve(chatId);
      request.onerror = () => reject(request.error);
    });
  }

  async getChatMetadata(chatId: string): Promise<any> {
    const db = await this.ensureDB();
    const transaction = db.transaction(['chatMetadata'], 'readonly');
    const metadataStore = transaction.objectStore('chatMetadata');

    return new Promise((resolve, reject) => {
      const request = metadataStore.get(chatId);
      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  async searchChats(query: string): Promise<{ id: string; title: string; updatedAt: number }[]> {
    const allChats = await this.listChats();
    const lowercaseQuery = query.toLowerCase();
    
    return allChats.filter(chat => 
      chat.title.toLowerCase().includes(lowercaseQuery)
    );
  }

  async exportAllChats(): Promise<any> {
    const chats = await this.listChats();
    const exportData = {
      version: 1,
      exportedAt: new Date().toISOString(),
      chats: [],
    };

    for (const chat of chats) {
      const messages = await this.loadChat(chat.id);
      const metadata = await this.getChatMetadata(chat.id);
      
      (exportData.chats as any[]).push({
        ...chat,
        messages,
        metadata,
      });
    }

    return exportData;
  }

  private extractTags(messages: CustomUIMessage[]): string[] {
    const tags = new Set<string>();
    
    messages.forEach(message => {
      message.parts.forEach(part => {
        if (part.type.startsWith('tool-')) {
          tags.add(part.type.replace('tool-', ''));
        }
        if (part.type === 'text') {
          // Extract hashtags or keywords
          const text = (part as any).text;
          const hashtags = text.match(/#\w+/g);
          if (hashtags) {
            hashtags.forEach((tag: string) => tags.add(tag.substring(1)));
          }
        }
      });
    });

    return Array.from(tags);
  }

  private generateSummary(messages: CustomUIMessage[]): string {
    const userMessages = messages
      .filter(m => m.role === 'user')
      .map(m => m.parts
        .filter(p => p.type === 'text')
        .map(p => (p as any).text)
        .join(' ')
      )
      .join(' ');

    // Simple summary generation (first 100 characters)
    return userMessages.substring(0, 100) + (userMessages.length > 100 ? '...' : '');
  }
}

// LocalStorage-based storage implementation (fallback)
export class LocalStorageChatStorage implements ChatStorage {
  private prefix = 'omnispace-chat-';

  async saveChat(chatId: string, messages: CustomUIMessage[]): Promise<void> {
    const chatData = {
      id: chatId,
      messages,
      updatedAt: Date.now(),
      title: this.generateTitle(messages),
    };

    localStorage.setItem(`${this.prefix}${chatId}`, JSON.stringify(chatData));
    
    // Update chat list
    const chatList = await this.listChats();
    const existingIndex = chatList.findIndex(c => c.id === chatId);
    
    if (existingIndex >= 0) {
      chatList[existingIndex] = {
        id: chatId,
        title: chatData.title,
        updatedAt: chatData.updatedAt,
      };
    } else {
      chatList.unshift({
        id: chatId,
        title: chatData.title,
        updatedAt: chatData.updatedAt,
      });
    }

    localStorage.setItem(`${this.prefix}list`, JSON.stringify(chatList));
  }

  async loadChat(chatId: string): Promise<CustomUIMessage[]> {
    const data = localStorage.getItem(`${this.prefix}${chatId}`);
    if (!data) {
      throw new Error(`Chat ${chatId} not found`);
    }

    const chatData = JSON.parse(data);
    return chatData.messages || [];
  }

  async deleteChat(chatId: string): Promise<void> {
    localStorage.removeItem(`${this.prefix}${chatId}`);
    
    const chatList = await this.listChats();
    const filteredList = chatList.filter(c => c.id !== chatId);
    localStorage.setItem(`${this.prefix}list`, JSON.stringify(filteredList));
  }

  async listChats(): Promise<{ id: string; title: string; updatedAt: number }[]> {
    const data = localStorage.getItem(`${this.prefix}list`);
    return data ? JSON.parse(data) : [];
  }

  async createChat(): Promise<string> {
    const chatId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    await this.saveChat(chatId, []);
    return chatId;
  }

  private generateTitle(messages: CustomUIMessage[]): string {
    const firstUserMessage = messages.find(m => m.role === 'user');
    if (!firstUserMessage) return 'New Chat';

    const text = firstUserMessage.parts
      .filter(p => p.type === 'text')
      .map(p => (p as any).text)
      .join(' ');

    return text.substring(0, 50) + (text.length > 50 ? '...' : '') || 'New Chat';
  }
}

// Factory function to create appropriate storage
export function createChatStorage(): ChatStorage {
  // Check if IndexedDB is available
  if (typeof window !== 'undefined' && window.indexedDB) {
    return new IndexedDBChatStorage();
  }
  
  // Fallback to localStorage
  return new LocalStorageChatStorage();
}
