import { Models } from 'appwrite';
import { 
  account, 
  databases, 
  storage,
  DATABASE_ID,
  USERS_COLLECTION_ID,
  SESSIONS_COLLECTION_ID,
  AVATARS_BUCKET_ID,
  UserProfile,
  UserSession,
  handleAppwriteError
} from './appwrite';

// Session management utilities
export class SessionManager {
  private static readonly SESSION_KEY = 'omnispace_session';
  private static readonly REFRESH_THRESHOLD = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

  /**
   * Check if the current session is valid
   */
  static async isSessionValid(): Promise<boolean> {
    try {
      const user = await account.get();
      return !!user;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current user session information
   */
  static async getCurrentSession(): Promise<Models.Session | null> {
    try {
      const sessions = await account.listSessions();
      return sessions.sessions.find(session => session.current) || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if session needs refresh
   */
  static async shouldRefreshSession(): Promise<boolean> {
    try {
      const session = await this.getCurrentSession();
      if (!session) return false;

      const expiryTime = new Date(session.expire).getTime();
      const currentTime = Date.now();
      const timeUntilExpiry = expiryTime - currentTime;

      return timeUntilExpiry < this.REFRESH_THRESHOLD;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get all active sessions for the current user
   */
  static async getAllSessions(): Promise<Models.Session[]> {
    try {
      const sessions = await account.listSessions();
      return sessions.sessions;
    } catch (error) {
      console.error('Failed to get sessions:', error);
      return [];
    }
  }

  /**
   * Track user session in database
   */
  static async trackSession(deviceInfo: {
    userAgent: string;
    ip: string;
    location?: string;
  }): Promise<UserSession | null> {
    try {
      const user = await account.get();
      const session = await this.getCurrentSession();
      
      if (!user || !session) return null;

      const sessionData: Omit<UserSession, '$id' | '$createdAt' | '$updatedAt'> = {
        userId: user.$id,
        sessionId: session.$id,
        deviceInfo: {
          ...deviceInfo,
          device: this.getDeviceType(deviceInfo.userAgent),
          browser: this.getBrowserName(deviceInfo.userAgent),
          os: this.getOperatingSystem(deviceInfo.userAgent),
        },
        activity: {
          loginTime: new Date().toISOString(),
          lastActivity: new Date().toISOString(),
          actions: 1,
          vmAccessed: [],
        },
        status: 'active',
        expiresAt: session.expire,
      };

      const trackedSession = await databases.createDocument(
        DATABASE_ID,
        SESSIONS_COLLECTION_ID,
        session.$id,
        sessionData
      );

      return trackedSession as UserSession;
    } catch (error) {
      console.error('Failed to track session:', error);
      return null;
    }
  }

  /**
   * Update session activity
   */
  static async updateSessionActivity(action?: string, vmId?: string): Promise<void> {
    try {
      const session = await this.getCurrentSession();
      if (!session) return;

      const updates: any = {
        'activity.lastActivity': new Date().toISOString(),
        'activity.actions': { $inc: 1 },
      };

      if (vmId) {
        updates['activity.vmAccessed'] = { $addToSet: vmId };
      }

      await databases.updateDocument(
        DATABASE_ID,
        SESSIONS_COLLECTION_ID,
        session.$id,
        updates
      );
    } catch (error) {
      console.error('Failed to update session activity:', error);
    }
  }

  /**
   * Clean up expired sessions
   */
  static async cleanupExpiredSessions(): Promise<void> {
    try {
      const user = await account.get();
      if (!user) return;

      const sessions = await databases.listDocuments(
        DATABASE_ID,
        SESSIONS_COLLECTION_ID,
        [`userId=${user.$id}`, `status=active`]
      );

      const now = new Date();
      const expiredSessions = sessions.documents.filter(session => 
        new Date(session.expiresAt) < now
      );

      for (const session of expiredSessions) {
        await databases.updateDocument(
          DATABASE_ID,
          SESSIONS_COLLECTION_ID,
          session.$id,
          { status: 'expired' }
        );
      }
    } catch (error) {
      console.error('Failed to cleanup expired sessions:', error);
    }
  }

  // Helper methods for device detection
  private static getDeviceType(userAgent: string): string {
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'Mobile';
    } else if (/Tablet/.test(userAgent)) {
      return 'Tablet';
    }
    return 'Desktop';
  }

  private static getBrowserName(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private static getOperatingSystem(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }
}

// User data utilities
export class UserDataManager {
  /**
   * Get user profile with caching
   */
  static async getUserProfile(userId: string, useCache = true): Promise<UserProfile | null> {
    try {
      const profile = await databases.getDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId
      );
      return profile as UserProfile;
    } catch (error) {
      console.error('Failed to get user profile:', error);
      return null;
    }
  }

  /**
   * Update user profile with validation
   */
  static async updateUserProfile(
    userId: string, 
    updates: Partial<UserProfile>
  ): Promise<UserProfile | null> {
    try {
      // Validate updates
      const validatedUpdates = this.validateProfileUpdates(updates);
      
      const updatedProfile = await databases.updateDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId,
        validatedUpdates
      );

      return updatedProfile as UserProfile;
    } catch (error) {
      console.error('Failed to update user profile:', error);
      throw handleAppwriteError(error);
    }
  }

  /**
   * Get user avatar URL
   */
  static getUserAvatarUrl(avatarId: string, width = 200, height = 200): string | null {
    try {
      return storage.getFilePreview(AVATARS_BUCKET_ID, avatarId, width, height).toString();
    } catch (error) {
      return null;
    }
  }

  /**
   * Upload user avatar
   */
  static async uploadUserAvatar(userId: string, file: File): Promise<string> {
    try {
      // Validate file
      this.validateAvatarFile(file);

      // Delete existing avatar if it exists
      const profile = await this.getUserProfile(userId);
      if (profile?.avatar) {
        try {
          await storage.deleteFile(AVATARS_BUCKET_ID, profile.avatar);
        } catch (error) {
          // Ignore error if file doesn't exist
        }
      }

      // Upload new avatar
      const fileId = `avatar_${userId}_${Date.now()}`;
      const uploadedFile = await storage.createFile(AVATARS_BUCKET_ID, fileId, file);

      // Update profile with new avatar
      await this.updateUserProfile(userId, { avatar: uploadedFile.$id });

      return uploadedFile.$id;
    } catch (error) {
      console.error('Failed to upload avatar:', error);
      throw handleAppwriteError(error);
    }
  }

  /**
   * Delete user avatar
   */
  static async deleteUserAvatar(userId: string): Promise<void> {
    try {
      const profile = await this.getUserProfile(userId);
      if (!profile?.avatar) return;

      await storage.deleteFile(AVATARS_BUCKET_ID, profile.avatar);
      await this.updateUserProfile(userId, { avatar: undefined });
    } catch (error) {
      console.error('Failed to delete avatar:', error);
      throw handleAppwriteError(error);
    }
  }

  /**
   * Get user usage statistics
   */
  static async getUserUsageStats(userId: string): Promise<UserProfile['usage'] | null> {
    try {
      const profile = await this.getUserProfile(userId);
      return profile?.usage || null;
    } catch (error) {
      console.error('Failed to get usage stats:', error);
      return null;
    }
  }

  /**
   * Update user usage statistics
   */
  static async updateUserUsage(
    userId: string, 
    updates: Partial<UserProfile['usage']>
  ): Promise<void> {
    try {
      const profile = await this.getUserProfile(userId);
      if (!profile) return;

      const updatedUsage = {
        ...profile.usage,
        ...updates,
        lastActivity: new Date().toISOString(),
      };

      await this.updateUserProfile(userId, { usage: updatedUsage });
    } catch (error) {
      console.error('Failed to update usage stats:', error);
    }
  }

  // Validation helpers
  private static validateProfileUpdates(updates: Partial<UserProfile>): Partial<UserProfile> {
    const validated: Partial<UserProfile> = {};

    if (updates.name !== undefined) {
      if (typeof updates.name === 'string' && updates.name.length >= 2 && updates.name.length <= 50) {
        validated.name = updates.name.trim();
      }
    }

    if (updates.bio !== undefined) {
      if (typeof updates.bio === 'string' && updates.bio.length <= 500) {
        validated.bio = updates.bio.trim();
      }
    }

    if (updates.company !== undefined) {
      if (typeof updates.company === 'string' && updates.company.length <= 100) {
        validated.company = updates.company.trim();
      }
    }

    if (updates.location !== undefined) {
      if (typeof updates.location === 'string' && updates.location.length <= 100) {
        validated.location = updates.location.trim();
      }
    }

    if (updates.website !== undefined) {
      if (typeof updates.website === 'string') {
        if (updates.website === '' || this.isValidUrl(updates.website)) {
          validated.website = updates.website;
        }
      }
    }

    if (updates.preferences !== undefined) {
      validated.preferences = updates.preferences;
    }

    if (updates.avatar !== undefined) {
      validated.avatar = updates.avatar;
    }

    return validated;
  }

  private static validateAvatarFile(file: File): void {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Please upload a JPEG, PNG, or WebP image.');
    }

    if (file.size > maxSize) {
      throw new Error('File size too large. Please upload an image smaller than 5MB.');
    }
  }

  private static isValidUrl(string: string): boolean {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }
}

// Authentication state utilities
export class AuthStateManager {
  /**
   * Check if user has required permissions
   */
  static hasPermission(user: UserProfile, permission: string): boolean {
    // Implement permission checking logic based on user role/plan
    switch (permission) {
      case 'create_vm':
        return user.subscription.status === 'active';
      case 'access_ai':
        return ['professional', 'enterprise'].includes(user.subscription.plan);
      case 'admin_access':
        return user.subscription.plan === 'enterprise';
      default:
        return false;
    }
  }

  /**
   * Get user display name
   */
  static getDisplayName(user: UserProfile): string {
    return user.name || user.email.split('@')[0];
  }

  /**
   * Get user initials for avatar
   */
  static getUserInitials(name: string): string {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  /**
   * Format subscription status
   */
  static formatSubscriptionStatus(status: string): string {
    switch (status) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'cancelled':
        return 'Cancelled';
      case 'past_due':
        return 'Past Due';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get plan features
   */
  static getPlanFeatures(plan: string): string[] {
    switch (plan) {
      case 'starter':
        return ['2 VMs', '10GB Storage', 'Community Support'];
      case 'professional':
        return ['10 VMs', '100GB Storage', 'AI Features', 'Email Support'];
      case 'enterprise':
        return ['Unlimited VMs', '1TB+ Storage', 'Advanced AI', '24/7 Support', 'Custom Integrations'];
      default:
        return [];
    }
  }
}
