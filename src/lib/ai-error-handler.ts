import { 
  APICallError, 
  InvalidPromptError, 
  NoSuchModelError, 
  TooManyEmbeddingValuesError,
  UnsupportedFunctionalityError 
} from 'ai';

export interface AIErrorResponse {
  error: string;
  code: string;
  details?: any;
  retryable: boolean;
}

export function handleAIError(error: unknown): AIErrorResponse {
  console.error('AI Error:', error);

  // Handle AI SDK specific errors
  if (error instanceof APICallError) {
    return {
      error: 'API call failed',
      code: 'API_CALL_ERROR',
      details: {
        message: error.message,
        statusCode: error.statusCode,
        responseHeaders: error.responseHeaders,
      },
      retryable: error.statusCode >= 500 || error.statusCode === 429,
    };
  }

  if (error instanceof InvalidPromptError) {
    return {
      error: 'Invalid prompt provided',
      code: 'INVALID_PROMPT',
      details: { message: error.message },
      retryable: false,
    };
  }

  if (error instanceof NoSuchModelError) {
    return {
      error: 'Model not found',
      code: 'NO_SUCH_MODEL',
      details: { message: error.message },
      retryable: false,
    };
  }

  if (error instanceof TooManyEmbeddingValuesError) {
    return {
      error: 'Too many embedding values',
      code: 'TOO_MANY_EMBEDDING_VALUES',
      details: { message: error.message },
      retryable: false,
    };
  }

  if (error instanceof UnsupportedFunctionalityError) {
    return {
      error: 'Unsupported functionality',
      code: 'UNSUPPORTED_FUNCTIONALITY',
      details: { message: error.message },
      retryable: false,
    };
  }

  // Handle generic errors
  if (error instanceof Error) {
    return {
      error: error.message || 'An unexpected error occurred',
      code: 'GENERIC_ERROR',
      details: { name: error.name, stack: error.stack },
      retryable: false,
    };
  }

  // Handle unknown errors
  return {
    error: 'An unknown error occurred',
    code: 'UNKNOWN_ERROR',
    details: { error: String(error) },
    retryable: false,
  };
}

export function createErrorResponse(error: unknown, status: number = 500): Response {
  const errorResponse = handleAIError(error);
  return Response.json(errorResponse, { status });
}

// Retry utility for retryable errors
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: unknown;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      const errorResponse = handleAIError(error);
      
      if (!errorResponse.retryable || attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
